#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法适配器模块
为三个钻井算法提供统一的接口适配

作者：AI Assistant
创建时间：2025-01-27
"""

import os
import sys
import pandas as pd
import numpy as np
import subprocess
import re
import tempfile
import logging
from typing import Dict, List, Tuple, Optional
from abc import ABC, abstractmethod

from unified_config import CONFIG
from data_segmentation import DataSegmentation

logger = logging.getLogger(__name__)


class BaseAlgorithmAdapter(ABC):
    """算法适配器基类"""
    
    def __init__(self, config: Dict):
        self.config = config
        
    @abstractmethod
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        pass
    
    @abstractmethod
    def run_algorithm(self, data_file: str) -> Dict:
        """运行算法"""
        pass
    
    @abstractmethod
    def parse_output(self, output: str) -> Dict:
        """解析算法输出"""
        pass


class PrecursorDetectionAdapter(BaseAlgorithmAdapter):
    """前驱信号检测算法适配器"""

    def __init__(self, config: Dict = None):
        super().__init__(config or CONFIG.PRECURSOR_CONFIG)
        self.segmentation = DataSegmentation(segment_duration_minutes=3)

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        前驱信号检测数据预处理
        确保数据符合10维特征要求
        """
        logger.debug("前驱信号检测数据预处理")

        # 确保包含所有必要的列
        required_cols = CONFIG.STANDARD_FEATURES
        processed_data = pd.DataFrame()

        for col in required_cols:
            if col in data.columns:
                processed_data[col] = data[col]
            elif col == 'date':
                # 如果没有时间列，创建序号
                processed_data[col] = range(len(data))
            else:
                # 数值特征用0填充
                processed_data[col] = 0.0
                logger.warning(f"前驱信号检测缺失特征 {col}，用0填充")

        return processed_data

    def run_algorithm(self, data_file: str) -> Dict:
        """运行前驱信号检测算法（支持片段处理）"""
        logger.info(f"运行前驱信号检测: {data_file}")

        try:
            # 1. 数据分割（为前驱信号检测创建特殊文件夹结构）
            segment_dir, segment_files = self.segmentation.segment_data_for_precursor(data_file)
            logger.info(f"前驱信号检测数据分割完成，共{len(segment_files)}个片段")

            if not segment_files:
                return {"success": False, "error": "没有生成有效的数据片段"}

            # 2. 批量预测所有片段
            segment_results = []

            for segment_file in segment_files:
                segment_result = self._run_single_segment(segment_file)
                segment_result['filename'] = os.path.basename(segment_file)
                segment_results.append(segment_result)

            # 3. 合并片段结果
            merged_result = self.segmentation.merge_segment_results(
                segment_results, 'precursor'
            )

            merged_result["success"] = True
            merged_result["segment_results"] = segment_results

            return merged_result

        except Exception as e:
            logger.error(f"前驱信号检测异常: {str(e)}")
            return {"success": False, "error": str(e)}

        finally:
            # 清理临时文件
            self.segmentation.cleanup_temp_dirs()

    def _run_single_segment(self, segment_file: str) -> Dict:
        """运行单个片段的前驱信号检测"""
        try:
            # 对于EarlysignaldetLoader，root_path应该指向包含normal和earlysignal2文件夹的目录
            # segment_file的路径结构是: root_path/normal/filename.csv
            segment_dir = os.path.dirname(segment_file)  # normal文件夹
            root_path = os.path.dirname(segment_dir)     # 包含normal和earlysignal2的根目录

            # 构建命令，使用Earlysignaldet数据类型（与训练时一致）
            cmd = [
                "python", self.config['script_path'],
                f"--task_name={self.config['task_name']}",
                "--is_training=0",
                f"--root_path={root_path}",  # 指向包含normal和earlysignal2的根目录
                f"--model={self.config['model']}",
                "--data=Earlysignaldet",  # 使用与训练时一致的数据类型
                "--features=M",
                f"--seq_len={self.config['seq_len']}",
                f"--enc_in={self.config['enc_in']}",
                f"--d_model={self.config['d_model']}",
                f"--batch_size={self.config['batch_size']}"
            ]

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")

            if result.returncode != 0:
                logger.warning(f"片段检测失败 {segment_file}: {result.stderr}")
                return {"success": False, "risk": 0.0, "label": 0, "error": result.stderr}

            # 解析输出
            parsed_result = self.parse_output(result.stdout)
            parsed_result["success"] = True

            return parsed_result

        except Exception as e:
            logger.warning(f"片段检测异常 {segment_file}: {str(e)}")
            return {"success": False, "risk": 0.0, "label": 0, "error": str(e)}
    
    def parse_output(self, output: str) -> Dict:
        """解析前驱信号检测输出"""
        # 这里需要根据实际的前驱信号检测输出格式进行解析
        # 目前使用默认值，实际使用时需要根据真实输出调整
        
        # 尝试从输出中提取风险分数和标签
        risk_score = 0.5  # 默认值
        predicted_label = 0  # 默认值
        
        # 查找可能的输出模式
        risk_patterns = [
            r'risk[:\s]+([0-9.]+)',
            r'score[:\s]+([0-9.]+)',
            r'confidence[:\s]+([0-9.]+)'
        ]
        
        label_patterns = [
            r'label[:\s]+([01])',
            r'prediction[:\s]+([01])',
            r'result[:\s]+([01])'
        ]
        
        for pattern in risk_patterns:
            match = re.search(pattern, output, re.IGNORECASE)
            if match:
                risk_score = float(match.group(1))
                break
        
        for pattern in label_patterns:
            match = re.search(pattern, output, re.IGNORECASE)
            if match:
                predicted_label = int(match.group(1))
                break
        
        return {
            "risk": risk_score,
            "label": predicted_label
        }


class AnomalyDetectionAdapter(BaseAlgorithmAdapter):
    """异常检测算法适配器"""

    def __init__(self, config: Dict = None):
        super().__init__(config or CONFIG.ANOMALY_CONFIG)
        self.segmentation = DataSegmentation(segment_duration_minutes=3)
        
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        异常检测数据预处理
        将10维特征扩展到12维，并转换为NPY格式要求
        """
        logger.debug("异常检测数据预处理")
        
        # 获取数值特征（去除date列）
        numeric_cols = [col for col in CONFIG.STANDARD_FEATURES if col != 'date']
        numeric_data = data[numeric_cols].copy()
        
        # 扩展到12维（异常检测要求）
        current_dims = len(numeric_cols)
        if current_dims < 12:
            # 用0填充缺失维度
            for i in range(current_dims, 12):
                numeric_data[f'feature_{i}'] = 0.0
                logger.debug(f"异常检测添加填充特征 feature_{i}")
        
        # 添加时间戳列（异常检测要求最后一列为时间戳）
        if 'date' in data.columns:
            numeric_data['timestamp'] = data['date']
        else:
            numeric_data['timestamp'] = range(len(data))
        
        return numeric_data
    
    def run_algorithm(self, data_file: str) -> Dict:
        """运行异常检测算法（支持片段处理）"""
        logger.info(f"运行异常检测: {data_file}")

        try:
            # 1. 数据分割
            segment_dir, segment_files = self.segmentation.segment_data(data_file)
            logger.info(f"异常检测数据分割完成，共{len(segment_files)}个片段")

            if not segment_files:
                return {"success": False, "error": "没有生成有效的数据片段"}

            # 2. 批量预测所有片段
            segment_results = []

            for segment_file in segment_files:
                segment_result = self._run_single_segment(segment_file)
                segment_result['filename'] = os.path.basename(segment_file)
                segment_results.append(segment_result)

            # 3. 合并片段结果
            merged_result = self.segmentation.merge_segment_results(
                segment_results, 'anomaly'
            )

            merged_result["success"] = True
            merged_result["segment_results"] = segment_results

            return merged_result

        except Exception as e:
            logger.error(f"异常检测异常: {str(e)}")
            return {"success": False, "error": str(e)}

        finally:
            # 清理临时文件
            self.segmentation.cleanup_temp_dirs()

    def _run_single_segment(self, segment_file: str) -> Dict:
        """运行单个片段的异常检测"""
        try:
            # 读取并预处理数据
            data = pd.read_csv(segment_file)
            processed_data = self.preprocess_data(data)

            # 创建临时NPY文件
            with tempfile.NamedTemporaryFile(suffix='.npy', delete=False) as tmp_file:
                temp_npy_file = tmp_file.name

                # 转换为numpy数组并保存
                npy_data = processed_data.values
                np.save(temp_npy_file, npy_data)

            try:
                # 构建命令
                cmd = [
                    "python", self.config['script_path'],
                    f"--task_name={self.config['task_name']}",
                    "--is_training=0",
                    f"--root_path={os.path.dirname(temp_npy_file)}",
                    f"--test_file={os.path.basename(temp_npy_file)}",
                    f"--model={self.config['model']}",
                    "--data=anomaly_detection",
                    f"--seq_len={self.config['seq_len']}",
                    f"--enc_in={self.config['enc_in']}",
                    f"--d_model={self.config['d_model']}",
                    "--no_label"
                ]

                # 执行命令
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")

                if result.returncode != 0:
                    logger.warning(f"片段异常检测失败 {segment_file}: {result.stderr}")
                    return {"success": False, "risk": 0.0, "label": 0, "error": result.stderr}

                # 解析输出
                parsed_result = self.parse_output(result.stdout)
                parsed_result["success"] = True

                return parsed_result

            finally:
                # 清理临时文件
                if os.path.exists(temp_npy_file):
                    os.unlink(temp_npy_file)

        except Exception as e:
            logger.warning(f"片段异常检测异常 {segment_file}: {str(e)}")
            return {"success": False, "risk": 0.0, "label": 0, "error": str(e)}
    
    def parse_output(self, output: str) -> Dict:
        """解析异常检测输出"""
        # 提取经验卡钻检测异常比例
        anomaly_ratio_match = re.search(r'经验卡钻检测异常比例: (\d+\.\d+)%', output)
        anomaly_ratio = float(anomaly_ratio_match.group(1)) if anomaly_ratio_match else 0.0
        
        # 将异常比例转换为风险分数和标签
        risk_score = anomaly_ratio / 100.0
        predicted_label = 1 if anomaly_ratio > self.config['anomaly_threshold'] else 0
        
        return {
            "risk": risk_score,
            "label": predicted_label,
            "anomaly_ratio": anomaly_ratio
        }


class ExpertSystemAdapter(BaseAlgorithmAdapter):
    """专家经验系统适配器"""

    def __init__(self, config: Dict = None):
        super().__init__(config or CONFIG.EXPERT_CONFIG)
        self.segmentation = DataSegmentation(segment_duration_minutes=3)
        
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        专家系统数据预处理
        重命名列以匹配专家系统要求
        """
        logger.debug("专家系统数据预处理")
        
        # 重命名列
        expert_data = data.rename(columns={
            'DEP': 'Well_Depth',
            'BITDEP': 'Bit_Depth', 
            'HOKHEI': 'Hook_Height',
            'HKLD': 'Hookload',
            'RPM': 'RPM',
            'TOR': 'Torque',
            'SPP': 'Standpipe_Pressure',
            'date': 'date'
        })
        
        # 添加缺失的流量列（专家系统需要）
        if 'Flow_In' not in expert_data.columns:
            expert_data['Flow_In'] = self.config['default_flow_in']
        if 'Flow_Out' not in expert_data.columns:
            expert_data['Flow_Out'] = self.config['default_flow_out']
        
        return expert_data
    
    def run_algorithm(self, data_file: str) -> Dict:
        """运行专家经验系统（支持片段处理）"""
        logger.info(f"运行专家经验系统: {data_file}")

        try:
            # 专家系统需要较长的数据来计算窗口特征
            # 如果数据太短，直接处理整个文件
            data = pd.read_csv(data_file)

            if len(data) < self.config['window_size'] * 2:
                # 数据太短，直接处理
                logger.info("数据长度较短，直接处理整个文件")
                return self._run_single_file(data_file)
            else:
                # 数据足够长，进行片段处理
                segment_dir, segment_files = self.segmentation.segment_data(data_file)
                logger.info(f"专家系统数据分割完成，共{len(segment_files)}个片段")

                if not segment_files:
                    return {"success": False, "error": "没有生成有效的数据片段"}

                # 批量预测所有片段
                segment_results = []

                for segment_file in segment_files:
                    segment_result = self._run_single_file(segment_file)
                    segment_result['filename'] = os.path.basename(segment_file)
                    segment_results.append(segment_result)

                # 合并片段结果
                merged_result = self.segmentation.merge_segment_results(
                    segment_results, 'expert'
                )

                merged_result["success"] = True
                merged_result["segment_results"] = segment_results

                return merged_result

        except Exception as e:
            logger.error(f"专家经验系统异常: {str(e)}")
            return {"success": False, "error": str(e)}

        finally:
            # 清理临时文件
            self.segmentation.cleanup_temp_dirs()

    def _run_single_file(self, data_file: str) -> Dict:
        """运行单个文件的专家系统分析"""
        try:
            # 读取并预处理数据
            data = pd.read_csv(data_file)
            processed_data = self.preprocess_data(data)

            # 执行专家规则计算
            scores = self._calculate_expert_scores(processed_data)

            # 计算结果
            avg_score = np.mean(scores) if scores else 0
            max_score = max(scores) if scores else 0

            # 转换为风险分数和标签
            risk_score = avg_score / 6.0  # 标准化到0-1
            predicted_label = 1 if max_score >= self.config['score_threshold'] else 0

            return {
                "success": True,
                "risk": risk_score,
                "label": predicted_label,
                "avg_score": avg_score,
                "max_score": max_score,
                "scores": scores
            }

        except Exception as e:
            logger.warning(f"专家系统文件处理异常 {data_file}: {str(e)}")
            return {"success": False, "risk": 0.0, "label": 0, "error": str(e)}
    
    def _calculate_expert_scores(self, data: pd.DataFrame) -> List[float]:
        """计算专家规则分数"""
        window = self.config['window_size']
        rules = self.config['rules']
        scores = []
        
        for i in range(window, len(data)):
            w = data.iloc[i - window:i]
            row = data.iloc[i]
            
            # 计算特征
            depth_change = w["Well_Depth"].max() - w["Well_Depth"].min()
            bit_change = w["Bit_Depth"].max() - w["Bit_Depth"].min()
            rpm_now = row["RPM"]
            flow_diff = (w["Flow_In"] - w["Flow_Out"]).mean()
            hook_height_change = w["Hook_Height"].max() - w["Hook_Height"].min()
            hookload_change = w["Hookload"].max() - w["Hookload"].min()
            flow_rpm_pattern = 1 if rpm_now < rules['rpm_threshold'] and flow_diff > rules['flow_diff_threshold'] else 0
            
            # 专家规则打分
            score = 0
            if depth_change < rules['depth_change_threshold']: score += 1
            if bit_change < rules['bit_change_threshold']: score += 1
            if rpm_now < rules['rpm_threshold']: score += 1
            if hook_height_change < rules['hook_height_change_threshold']: score += 1
            if hookload_change > rules['hookload_change_threshold']: score += 1
            if flow_rpm_pattern == 1: score += 1
            
            scores.append(score)
        
        return scores
    
    def parse_output(self, output: str) -> Dict:
        """专家系统不需要解析外部输出"""
        return {}


# 适配器工厂
class AdapterFactory:
    """适配器工厂类"""
    
    _adapters = {
        'precursor': PrecursorDetectionAdapter,
        'anomaly': AnomalyDetectionAdapter,
        'expert': ExpertSystemAdapter
    }
    
    @classmethod
    def create_adapter(cls, algorithm_type: str, config: Dict = None):
        """创建算法适配器"""
        if algorithm_type not in cls._adapters:
            raise ValueError(f"不支持的算法类型: {algorithm_type}")
        
        adapter_class = cls._adapters[algorithm_type]
        return adapter_class(config)
    
    @classmethod
    def get_supported_algorithms(cls) -> List[str]:
        """获取支持的算法类型"""
        return list(cls._adapters.keys())
