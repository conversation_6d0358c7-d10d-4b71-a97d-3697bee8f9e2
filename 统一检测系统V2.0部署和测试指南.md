# 统一检测系统V2.0部署和测试指南

## 📋 概述

本文档提供统一钻井检测系统V2.0的完整部署和测试指南，帮助用户将代码实现指南中的设计转化为实际可运行的系统。

## 🚀 部署步骤

### 第一步：环境准备

#### 1.1 创建项目目录结构
```bash
# 在当前工作目录下创建V2.0系统目录
mkdir unified_detection_system_v2
cd unified_detection_system_v2

# 创建核心目录结构
mkdir -p core config utils
touch core/__init__.py config/__init__.py utils/__init__.py
```

#### 1.2 Python环境配置
```bash
# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 升级pip
python -m pip install --upgrade pip
```

#### 1.3 安装依赖包
```bash
# 创建requirements.txt文件
cat > requirements.txt << EOF
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
PyYAML>=6.0
torch>=1.9.0
torchvision>=0.10.0
pathlib2>=2.3.0
tqdm>=4.62.0
EOF

# 安装依赖
pip install -r requirements.txt
```

### 第二步：代码文件创建

#### 2.1 配置管理模块
```bash
# 创建 config/config.py
# 将《统一检测系统V2.0代码实现指南.md》中的配置管理代码复制到此文件
```

#### 2.2 核心模块文件
```bash
# 创建核心模块文件
touch core/data_processor.py
touch core/algorithm_adapters.py
touch core/fusion_engine.py
touch core/batch_processor.py

# 将代码实现指南中对应的代码复制到各个文件中
```

#### 2.3 主入口和配置文件
```bash
# 创建主入口文件
touch main.py

# 创建配置文件
touch config.yaml
```

### 第三步：算法路径配置

#### 3.1 检查现有算法目录
```bash
# 确认当前目录下的算法文件夹
ls -la
# 应该看到：
# - 前驱信号检测/
# - 异常检测/
# - 专家经验/
```

#### 3.2 配置config.yaml文件
```yaml
# 根据实际路径修改config.yaml
system:
  version: "2.0"
  name: "统一钻井检测系统V2.0"

weights:
  precursor: 0.5
  anomaly: 0.3
  expert: 0.2

algorithms:
  precursor:
    model_path: "./前驱信号检测/checkpoints"
    data_path: "./前驱信号检测/dataset2"
    sequence_length: 152
    features: 10
    
  anomaly:
    model_path: "./异常检测/checkpoints"
    train_data_path: "./异常检测/dataset/train自201H54-3.npy"
    anomaly_ratio: 1.0
    features: 12
    
  expert:
    window_size: 30
    score_threshold: 4
    rules_config:
      depth_change_threshold: 0.001
      bit_change_threshold: 0.001
      rpm_threshold: 5
      hook_height_change_threshold: 0.01
      hookload_change_threshold: 3
      flow_diff_threshold: 15

data_processing:
  csv_encoding: "utf-8"
  npy_precision: "float32"
  missing_value_strategy: "default"

output:
  save_detailed_results: true
  save_summary_report: true
  result_format: "json"
```

## 🧪 测试步骤

### 第一步：基础功能测试

#### 1.1 配置加载测试
```bash
# 测试配置文件加载
python -c "
from config.config import config_manager
print('配置加载成功')
print(f'系统版本: {config_manager.get(\"system.version\")}')
print(f'算法权重: {config_manager.get(\"weights\")}')
"
```

#### 1.2 数据预处理测试
```bash
# 测试数据预处理模块
python -c "
from core.data_processor import DataProcessorV2
processor = DataProcessorV2()
print('数据预处理器初始化成功')
"
```

#### 1.3 算法适配器测试
```bash
# 测试算法适配器
python -c "
from core.algorithm_adapters import SimplifiedAnomalyDetectionAdapter, PureRulesExpertAdapter, PrecursorDetectionAdapter
print('异常检测适配器:', SimplifiedAnomalyDetectionAdapter())
print('专家规则适配器:', PureRulesExpertAdapter())
print('前驱信号适配器:', PrecursorDetectionAdapter())
print('所有适配器初始化成功')
"
```

### 第二步：单文件测试

#### 2.1 准备测试数据
```bash
# 使用现有的CSV文件进行测试
# 确认测试文件存在
ls -la *.csv
# 例如：充浅1实时数据.csv, 泸201H1-4实时数据.csv
```

#### 2.2 单文件处理测试
```bash
# 测试单文件处理（使用现有CSV文件）
python main.py --input 充浅1实时数据.csv --output test_results/

# 检查输出结果
ls -la test_results/
cat test_results/result.json
```

#### 2.3 验证输出格式
```bash
# 验证JSON输出格式
python -c "
import json
with open('test_results/result.json', 'r', encoding='utf-8') as f:
    result = json.load(f)
print('输出格式验证:')
print(f'成功状态: {result.get(\"success\")}')
print(f'风险分数: {result.get(\"risk\")}')
print(f'预测标签: {result.get(\"label\")}')
print(f'算法结果: {list(result.get(\"algorithm_results\", {}).keys())}')
"
```

### 第三步：批量处理测试

#### 3.1 准备批量测试数据
```bash
# 创建测试数据文件夹
mkdir -p batch_test_data
cp *.csv batch_test_data/

# 确认批量数据
ls -la batch_test_data/
```

#### 3.2 批量处理测试
```bash
# 执行批量处理
python main.py --input batch_test_data/ --output batch_results/ --mode batch

# 检查批量结果
ls -la batch_results/
```

#### 3.3 验证批量结果
```bash
# 查看详细结果
head -20 batch_results/detailed_results.json

# 查看汇总报告
cat batch_results/summary_report.json

# 查看CSV报告
head -10 batch_results/results_summary.csv
```

## 🔍 故障排除指南

### 常见问题和解决方案

#### 问题1：模块导入错误
```bash
# 错误：ModuleNotFoundError: No module named 'config'
# 解决：确保在项目根目录下运行，并检查__init__.py文件
pwd
ls -la core/__init__.py config/__init__.py utils/__init__.py
```

#### 问题2：算法路径不存在
```bash
# 错误：算法运行脚本不存在
# 解决：检查并修正config.yaml中的路径配置
ls -la 前驱信号检测/run.py
ls -la 异常检测/run.py
ls -la 专家经验/run6_test.py
```

#### 问题3：依赖包版本冲突
```bash
# 错误：包版本不兼容
# 解决：使用虚拟环境并重新安装
deactivate
rm -rf venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

#### 问题4：数据格式错误
```bash
# 错误：CSV文件格式不正确
# 解决：检查CSV文件的列名和数据格式
python -c "
import pandas as pd
df = pd.read_csv('充浅1实时数据.csv')
print('CSV文件信息:')
print(f'形状: {df.shape}')
print(f'列名: {list(df.columns)}')
print(f'前5行:')
print(df.head())
"
```

#### 问题5：权限问题
```bash
# 错误：文件写入权限不足
# 解决：检查输出目录权限
mkdir -p test_output
chmod 755 test_output
```

## 📊 性能监控

### 系统性能检查

#### 1. 处理时间监控
```bash
# 记录处理时间
time python main.py --input 充浅1实时数据.csv --output time_test/
```

#### 2. 内存使用监控
```bash
# 监控内存使用（Linux/Mac）
/usr/bin/time -v python main.py --input batch_test_data/ --output memory_test/ --mode batch
```

#### 3. 算法成功率统计
```bash
# 分析批量处理结果
python -c "
import json
with open('batch_results/summary_report.json', 'r', encoding='utf-8') as f:
    summary = json.load(f)
print('算法成功率统计:')
for algo, stats in summary.get('algorithm_stats', {}).items():
    print(f'{algo}: {stats.get(\"success_rate\", 0):.2%}')
print(f'总体成功率: {summary.get(\"success_rate\", 0):.2%}')
"
```

## ✅ 验收标准

### 系统验收清单

#### 基础功能验收
- [ ] 配置文件正确加载
- [ ] 所有核心模块正常初始化
- [ ] 单文件处理功能正常
- [ ] 批量处理功能正常
- [ ] 输出格式符合预期

#### 算法功能验收
- [ ] 前驱信号检测算法正常运行
- [ ] 简化异常检测算法正常运行
- [ ] 纯规则专家系统正常运行
- [ ] 三算法融合结果正确

#### 错误处理验收
- [ ] 单算法失败时系统能继续运行
- [ ] 错误信息详细且有用
- [ ] 临时文件正确清理
- [ ] 异常情况下程序不崩溃

#### 输出质量验收
- [ ] JSON格式输出完整
- [ ] CSV格式报告清晰
- [ ] 汇总统计准确
- [ ] 处理进度显示正常

## 🎯 部署完成确认

### 最终验证步骤

1. **完整流程测试**：从单文件到批量处理的完整流程
2. **多种数据测试**：使用不同的CSV文件验证系统稳定性
3. **边界条件测试**：测试空文件、大文件、格式错误等边界情况
4. **长时间运行测试**：批量处理大量文件验证系统稳定性

### 部署成功标志

- ✅ 所有测试用例通过
- ✅ 系统能稳定处理实际数据
- ✅ 输出结果格式正确且有意义
- ✅ 错误处理机制有效
- ✅ 用户能够独立操作系统

完成以上所有步骤后，统一检测系统V2.0即可投入实际使用。
