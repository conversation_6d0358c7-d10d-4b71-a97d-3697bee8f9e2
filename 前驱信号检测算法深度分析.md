# 前驱信号检测算法深度分析

## 📋 概述

基于对前驱信号检测run.py及其完整调用链的深入分析，本文档详细说明该算法的真实运行机制、数据要求和输出格式。

## 🔍 运行流程分析

### 1. 主入口：run.py

#### 参数配置
```bash
python run.py \
  --task_name earlysignaldet \
  --is_training 0 \
  --root_path ./dataset2 \
  --model_id earlysignaldetection \
  --model PatchTST \
  --data Earlysignaldet \
  --e_layers 3 \
  --batch_size 16 \
  --d_model 128 \
  --d_ff 256 \
  --top_k 3 \
  --des 'Exp' \
  --itr 1 \
  --learning_rate 0.001 \
  --train_epochs 200 \
  --patience 50 \
  --do_predict
```

#### 关键执行路径（is_training=0时）
```python
# run.py 第164-188行
if args.is_training:
    # 训练流程...
else:
    ii = 0
    setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
        args.task_name,      # earlysignaldet
        args.model_id,       # earlysignaldetection
        args.model,          # PatchTST
        args.data,           # Earlysignaldet
        args.features,       # M
        args.seq_len,        # 96
        args.label_len,      # 48
        args.pred_len,       # 24
        args.d_model,        # 128
        args.n_heads,        # 8
        args.e_layers,       # 3
        args.d_layers,       # 1
        args.d_ff,           # 256
        args.factor,         # 1
        args.embed,          # timeF
        args.distil,         # True
        args.des,            # Exp
        ii                   # 0
    )
    
    exp = Exp_Earlysignaldet(args)  # 创建实验对象
    print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
    exp.test(setting, test=1)       # 执行测试
    
    if args.do_predict:
        print('>>>>>>>predicting : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
        exp.predict(setting, True)  # 执行预测
```

### 2. 实验类：Exp_Earlysignaldet

#### 初始化和模型构建
```python
class Exp_Earlysignaldet(Exp_Basic):
    def __init__(self, args):
        super(Exp_Earlysignaldet, self).__init__(args)

    def _build_model(self):
        # 关键：获取数据来确定模型参数
        train_data, train_loader = self._get_data(flag='TRAIN')
        test_data, test_loader = self._get_data(flag='TEST')
        
        # 动态设置序列长度
        self.args.seq_len = max(train_data.max_seq_len, test_data.max_seq_len)
        self.args.pred_len = 0
        self.args.num_class = 2  # 二分类
        
        # 创建PatchTST模型
        model = self.model_dict[self.args.model](self.args).float()
        return model
```

#### 数据获取机制
```python
def _get_data(self, flag):
    data_set, data_loader = data_provider(self.args, flag)
    return data_set, data_loader
```

### 3. 数据提供器：data_provider

#### 数据类型映射（data_factory.py）
```python
data_dict = {
    'Earlysignaldet': EarlysignaldetLoader,  # 关键映射
    # 其他数据类型...
}

def data_provider(args, flag):
    Data = data_dict[args.data]  # 获取EarlysignaldetLoader
    
    # 根据任务类型设置参数
    if args.task_name == 'earlysignaldet':
        data_set = Data(
            root_path=args.root_path,
            flag=flag  # 'TRAIN' 或 'TEST'
        )
        data_loader = DataLoader(
            data_set,
            batch_size=args.batch_size,
            shuffle=(flag == 'TRAIN'),
            drop_last=True
        )
    
    return data_set, data_loader
```

### 4. 核心数据加载器：EarlysignaldetLoader

#### 初始化流程
```python
class EarlysignaldetLoader(Dataset):
    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.flag = flag
        
        # Step 1: 获取所有文件路径和标签
        self.file_paths, self.labels = self.get_file_paths_and_labels(root_path)
        
        # Step 2: 收集分类特征值（CW和RIGSTA）
        self.cw_values, self.rigsta_values = self.collect_categorical_values(self.file_paths)
        
        # Step 3: 训练LabelEncoder
        self.cw_encoder = LabelEncoder()
        self.rigsta_encoder = LabelEncoder()
        self.cw_encoder.fit(self.cw_values)
        self.rigsta_encoder.fit(self.rigsta_values)
        
        # Step 4: 获取最大序列长度
        self.max_seq_len = self.get_max_length(self.file_paths)
        
        # Step 5: 数据分割（关键步骤）
        self.train_file, self.test_file, self.train_labels, self.test_labels = train_test_split(
            self.file_paths, self.labels, test_size=0.2, random_state=42
        )
        
        # Step 6: 根据flag选择数据集
        if self.flag == 'TRAIN':
            self.file_paths = self.train_file
            self.labels = self.train_labels
        else:  # 'TEST'
            self.file_paths = self.test_file
            self.labels = self.test_labels
            
        self.class_names = ['normal', 'earlysignal2']
        self.scaler = StandardScaler()
```

#### 文件夹结构要求
```python
def get_file_paths_and_labels(self, base_folder):
    """
    关键发现：必须有特定的文件夹结构
    """
    file_paths = []
    labels = []
    
    # 硬编码的文件夹名称
    for class_label, class_folder in enumerate(['normal', 'earlysignal2']):
        class_folder_path = os.path.join(base_folder, class_folder)
        
        # 扫描文件夹中的所有文件
        for file_name in os.listdir(class_folder_path):
            file_paths.append(os.path.join(class_folder_path, file_name))
            labels.append(class_label)  # normal=0, earlysignal2=1
    
    return file_paths, labels
```

#### 数据加载和预处理
```python
def __getitem__(self, idx):
    file_path = self.file_paths[idx]
    label = self.labels[idx]
    
    # 加载CSV文件
    data = pd.read_csv(file_path)
    
    # 删除不需要的列
    if 'CSIP' in data.columns:
        data = data.drop(columns=['CSIP'])
    
    # 分离时间序列数据和非时间特征
    time_series = data.drop(['date', 'CW', 'RIGSTA'], axis=1).values
    non_temp = data[['CW', 'RIGSTA']].values
    
    # 编码分类特征
    non_temp[:, 0] = self.cw_encoder.transform(non_temp[:, 0])
    non_temp[:, 1] = self.rigsta_encoder.transform(non_temp[:, 1])
    
    # 序列长度标准化（填充或截断）
    length = len(time_series)
    if length < self.max_seq_len:
        # 填充0
        padded_time_series = np.zeros((self.max_seq_len, time_series.shape[1]))
        padded_time_series[:length] = time_series
        
        padded_non_temp = np.zeros((self.max_seq_len, non_temp.shape[1]))
        padded_non_temp[:length] = non_temp
    else:
        # 截断
        padded_time_series = time_series[:self.max_seq_len]
        padded_non_temp = non_temp[:self.max_seq_len]
    
    # 标准化
    self.scaler.fit(padded_time_series)
    padded_time_series = self.scaler.transform(padded_time_series)
    
    return padded_time_series, padded_non_temp, label, file_path
```

### 5. 测试流程：exp.test()

#### 模型加载和预测
```python
def test(self, setting, test=1):
    test_data, test_loader = self._get_data(flag='TEST')
    
    if test:
        print('loading model')
        # 加载预训练模型
        self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))

    preds = []
    trues = []
    prediction_results = []
    
    self.model.eval()
    with torch.no_grad():
        for i, (batch_x, non_temp, label, filename) in enumerate(test_loader):
            batch_x = batch_x.float().to(self.device)
            label = label.to(self.device)

            # 模型前向传播
            outputs = self.model(batch_x, None, None, None)

            # 计算概率和预测
            preds.append(outputs.detach())
            prob = F.softmax(outputs, dim=1)
            risk = prob[:,1]  # 类别1的概率作为风险分数
            _, pred = torch.max(prob.data, 1)
            trues.append(label)
            
            # 保存每个样本的结果
            for j in range(batch_x.size(0)):
                prediction_results.append([
                    filename[j],      # 文件名
                    risk[j].item(),   # 风险分数 (0-1)
                    pred[j].item(),   # 预测标签 (0/1)
                    label[j].item()   # 真实标签
                ])
```

#### 输出格式和保存
```python
    # 计算评估指标
    preds = torch.cat(preds, 0)
    trues = torch.cat(trues, 0)
    probs = torch.nn.functional.softmax(preds)
    predictions = torch.argmax(probs, dim=1).cpu().numpy()
    trues = trues.flatten().cpu().numpy()
    
    accuracy = cal_accuracy(predictions, trues)
    precision = precision_score(trues, predictions, average='macro')
    recall = recall_score(trues, predictions, average='macro')
    f1 = f1_score(trues, predictions, average='macro')

    # 保存结果到CSV
    output_df = pd.DataFrame(prediction_results, columns=['Filename', 'Risk', 'Predicted_Label','GroundTruth'])
    output_df.to_csv('predictions' + self.args.model + '.csv', index=False, encoding='gbk')

    # 打印结果
    print('accuracy:{}'.format(accuracy))
    print('precision:{}'.format(precision))
    print('recall:{}'.format(recall))
    print('f1-score:{}'.format(f1))
```

### 6. 预测流程：exp.predict()

#### 预测数据加载
```python
def predict(self, setting, load=True):
    # 使用EarlysignalPred类加载预测数据
    pred_dataset = EarlysignalPred(r"D:\0temp\算法\前驱信号检测\dataset2\predicate2")
    pred_loader = DataLoader(pred_dataset, batch_size=1, shuffle=False)

    # 加载模型
    if load:
        path = os.path.join(self.args.checkpoints, setting)
        best_model_path = path + '/' + 'checkpoint.pth'
        self.model.load_state_dict(torch.load(best_model_path))

    predictions = []
    
    # 模型预测
    self.model.eval()
    with torch.no_grad():
        for i, (batch_x, filename) in enumerate(pred_loader):
            batch_x = batch_x.float().to(self.device)

            output = self.model(batch_x, None, None, None)
            probs = F.softmax(output, dim=1)
            risk = probs[0, 1]  # 风险分数
            _, pred = torch.max(probs.data, 1)  # 预测标签

            file_str = str(filename[0]) if isinstance(filename, (list, tuple)) else str(filename)
            predictions.append((file_str, risk.item(), pred.item()))
```

#### 结果排序和保存
```python
    # 按井号和时间排序
    def extract_well_number(name):
        match = re.search(r'(\d+)井', name)
        return int(match.group(1)) if match else float('inf')

    def extract_time(name):
        match = re.search(r'_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})', name)
        return match.group(1) if match else ""

    predictions.sort(key=lambda x: (extract_well_number(x[0]), extract_time(x[0])))

    # 保存结果
    output_df = pd.DataFrame(predictions, columns=['Filename', 'Risk', 'Predicted_Label'])
    output_df.to_csv('predictions.csv', index=False, encoding='gbk')
    print("预测完毕，结果已保存至 predictions.csv")
```

## 🎯 关键发现总结

### 1. 数据要求
- **文件夹结构**：必须有`root_path/normal/`和`root_path/earlysignal2/`两个子文件夹
- **文件格式**：CSV格式，包含时间序列特征和分类特征
- **必需列**：时间序列特征 + date + CW + RIGSTA
- **标签分配**：normal文件夹=标签0，earlysignal2文件夹=标签1

### 2. 数据处理机制
- **自动分割**：使用`train_test_split`将所有文件分为训练集(80%)和测试集(20%)
- **Flag选择**：根据flag参数('TRAIN'/'TEST')选择对应的数据子集
- **序列标准化**：填充或截断到统一长度，应用StandardScaler标准化
- **分类编码**：使用LabelEncoder对CW和RIGSTA进行编码

### 3. 模型输出
- **风险分数**：softmax概率中类别1的概率值 (0-1)
- **预测标签**：argmax结果 (0/1)
- **评估指标**：accuracy, precision, recall, f1-score
- **保存格式**：CSV文件，包含[Filename, Risk, Predicted_Label, GroundTruth]

### 4. 运行模式
- **测试模式**：使用测试集评估模型性能，输出评估指标
- **预测模式**：使用EarlysignalPred加载新数据进行预测，输出预测结果

### 5. 关键参数
- `--data Earlysignaldet`：必须使用此参数触发EarlysignaldetLoader
- `--task_name earlysignaldet`：必须使用此任务名
- `--is_training 0`：预测模式
- `--do_predict`：启用预测功能

## 🚀 融合系统设计建议

基于以上分析，统一融合系统需要：

1. **正确的文件夹结构创建**：为EarlysignaldetLoader创建normal/和earlysignal2/子文件夹
2. **准确的参数传递**：使用`--data Earlysignaldet`而非`--data custom`
3. **正确的输出解析**：提取accuracy, precision, recall等指标，以及风险分数
4. **适当的数据预处理**：确保CSV文件包含所需的列和格式

这个深入分析为设计真正有效的融合系统提供了准确的技术基础。
