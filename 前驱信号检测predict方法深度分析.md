# 前驱信号检测predict()方法深度分析

## 📋 概述

本文档专注分析前驱信号检测的`exp.predict()`方法，这是融合算法真正需要关注的部分，因为它处理的是统一的输入数据，而不是训练时的特殊文件夹结构。

## 🎯 为什么predict()是关键

### 训练vs预测的区别
- **训练/测试模式**：使用`EarlysignaldetLoader`，需要`normal/`和`earlysignal2/`文件夹结构
- **预测模式**：使用`EarlysignalPred`，直接处理单个文件夹中的CSV文件
- **融合系统需求**：统一输入数据格式，不可能为每个算法创建特殊文件夹结构

## 🔍 predict()方法完整分析

### 1. 方法签名和初始化

```python
def predict(self, setting, load=True):
    # 关键：使用EarlysignalPred而不是EarlysignaldetLoader
    pred_dataset = EarlysignalPred(r"D:\0temp\算法\前驱信号检测\dataset2\predicate2")
    pred_loader = DataLoader(pred_dataset, batch_size=1, shuffle=False)

    # 加载预训练模型
    if load:
        path = os.path.join(self.args.checkpoints, setting)
        best_model_path = path + '/' + 'checkpoint.pth'
        self.model.load_state_dict(torch.load(best_model_path))

    predictions = []
```

**关键发现**：
- 硬编码路径：`r"D:\0temp\算法\前驱信号检测\dataset2\predicate2"`
- 使用`EarlysignalPred`类，不需要文件夹结构
- 批量大小为1，逐个文件处理

### 2. EarlysignalPred类详细分析

#### 初始化方法
```python
class EarlysignalPred(Dataset):
    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.root_path = root_path
        self.scaler = StandardScaler()
        # 关键：直接扫描文件夹中的所有CSV文件
        self.file_names = [f for f in os.listdir(root_path) if f.endswith('.csv')]

    def __len__(self):
        return len(self.file_names)
```

**关键特点**：
- 简单的文件夹扫描，不需要子文件夹
- 只处理`.csv`文件
- 每个文件作为一个独立的样本

#### 数据加载方法（核心）
```python
def __getitem__(self, idx):
    if torch.is_tensor(idx):
        idx = idx.tolist()
    
    file_name = self.file_names[idx]
    file_path = os.path.join(self.root_path, self.file_names[idx])
    
    # 读取CSV文件
    data = pd.read_csv(file_path)
    
    # 删除CSIP列（如果存在）
    if 'CSIP' in data.columns:
        data = data.drop(columns=['CSIP'])

    # 关键：提取时间序列数据（去除date列）
    time_series = data.drop(['date'], axis=1).values
    stamp = data['date']  # 保存时间戳但不使用
    
    # 序列长度标准化
    length = len(time_series)
    if length < 152:
        # 填充到152长度，特征维度根据版本不同（9或11）
        padded_time_series = np.zeros((152, time_series.shape[1]))  # 动态维度
        padded_time_series[:length] = time_series
    else:
        # 截断到152长度
        padded_time_series = time_series[:152]

    # 标准化处理
    self.scaler.fit(padded_time_series)
    padded_time_series = self.scaler.transform(padded_time_series)

    return padded_time_series, file_name
```

**关键发现**：
1. **输入格式**：标准CSV文件，包含数值特征和date列
2. **特征处理**：去除date列，保留所有数值特征
3. **维度适应**：动态适应特征维度（代码中有9维和11维两个版本）
4. **长度标准化**：固定长度152，填充或截断
5. **标准化**：每个文件独立进行StandardScaler标准化
6. **返回值**：`(padded_time_series, file_name)` - 只有数据和文件名，没有标签

### 3. 模型预测流程

```python
# 模型预测
self.model.eval()
with torch.no_grad():
    for i, (batch_x, filename) in enumerate(pred_loader):
        batch_x = batch_x.float().to(self.device)

        # 模型前向传播
        output = self.model(batch_x, None, None, None)
        
        # 计算概率和预测
        probs = F.softmax(output, dim=1)
        risk = probs[0, 1]  # 类别1的概率作为风险分数
        _, pred = torch.max(probs.data, 1)  # 预测标签

        # 处理文件名
        file_str = str(filename[0]) if isinstance(filename, (list, tuple)) else str(filename)
        
        # 保存结果
        predictions.append((file_str, risk.item(), pred.item()))
```

**输出格式**：
- `file_str`：文件名字符串
- `risk.item()`：风险分数，0-1之间的浮点数
- `pred.item()`：预测标签，0或1

### 4. 结果处理和排序

```python
# 排序逻辑
def extract_well_number(name):
    """提取井号数字部分"""
    name = str(name)
    match = re.search(r'(\d+)井', name)
    return int(match.group(1)) if match else float('inf')

def extract_time(name):
    """提取时间部分"""
    name = str(name)
    match = re.search(r'_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})', name)
    return match.group(1) if match else ""

# 按井号和时间排序
predictions.sort(key=lambda x: (extract_well_number(x[0]), extract_time(x[0])))

# 保存结果
output_df = pd.DataFrame(predictions, columns=['Filename', 'Risk', 'Predicted_Label'])
output_df.to_csv('predictions.csv', index=False, encoding='gbk')
print("预测完毕，结果已保存至 predictions.csv")

return predictions
```

**最终输出**：
- CSV文件：`predictions.csv`
- 列名：`['Filename', 'Risk', 'Predicted_Label']`
- 编码：`gbk`
- 排序：按井号和时间排序

## 🎯 融合系统的关键启示

### 1. 输入数据格式要求

```python
# 标准CSV文件格式
required_columns = [
    'DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 
    'HKLD', 'RPM', 'TOR', 'SPP',  # 9个数值特征
    'date'  # 时间列（会被去除）
]

# 可选列（会被自动删除）
optional_columns = ['CSIP']

# 数据要求
data_requirements = {
    'format': 'CSV',
    'encoding': 'utf-8 or gbk',
    'features': '9-11个数值特征 + date列',
    'length': '任意长度（会标准化到152）',
    'missing_values': '需要预处理'
}
```

### 2. 数据预处理流程

```python
def preprocess_for_precursor_predict(csv_file):
    """为前驱信号检测预测准备数据"""
    
    # 1. 读取CSV文件
    data = pd.read_csv(csv_file)
    
    # 2. 删除不需要的列
    if 'CSIP' in data.columns:
        data = data.drop(columns=['CSIP'])
    
    # 3. 确保有date列
    if 'date' not in data.columns:
        # 如果没有date列，创建一个
        data['date'] = pd.date_range(start='2024-01-01', periods=len(data), freq='1min')
    
    # 4. 确保特征列存在
    required_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP']
    for feature in required_features:
        if feature not in data.columns:
            data[feature] = 0.0  # 填充缺失特征
    
    # 5. 重新排列列顺序（date列在最后）
    feature_cols = [col for col in data.columns if col != 'date']
    data = data[feature_cols + ['date']]
    
    return data
```

### 3. 调用接口设计

```python
class PrecursorPredictAdapter:
    def __init__(self, model_path, temp_dir):
        self.model_path = model_path
        self.temp_dir = temp_dir
    
    def predict_single_file(self, csv_file):
        """预测单个CSV文件"""
        
        # 1. 数据预处理
        processed_data = preprocess_for_precursor_predict(csv_file)
        
        # 2. 保存到临时文件
        temp_file = os.path.join(self.temp_dir, 'temp_input.csv')
        processed_data.to_csv(temp_file, index=False)
        
        # 3. 调用predict方法
        cmd = [
            'python', 'run.py',
            '--task_name', 'earlysignaldet',
            '--is_training', '0',
            '--model', 'PatchTST',
            '--data', 'Earlysignaldet',
            '--do_predict'
        ]
        
        # 4. 修改predict方法中的硬编码路径
        # 需要修改exp_earlysignaldet.py中的路径为self.temp_dir
        
        # 5. 执行并解析结果
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # 6. 解析predictions.csv
        predictions_df = pd.read_csv('predictions.csv', encoding='gbk')
        
        return {
            'filename': predictions_df.iloc[0]['Filename'],
            'risk': predictions_df.iloc[0]['Risk'],
            'label': predictions_df.iloc[0]['Predicted_Label']
        }
```

### 4. 关键修改需求

为了在融合系统中正确使用predict()方法，需要进行以下修改：

1. **路径参数化**：
```python
# 修改exp_earlysignaldet.py中的硬编码路径
def predict(self, setting, load=True, predict_path=None):
    if predict_path is None:
        predict_path = r"D:\0temp\算法\前驱信号检测\dataset2\predicate2"
    
    pred_dataset = EarlysignalPred(predict_path)
    # ... 其余代码不变
```

2. **输出路径控制**：
```python
# 允许指定输出文件路径
def predict(self, setting, load=True, predict_path=None, output_path=None):
    # ... 预测逻辑 ...
    
    if output_path is None:
        output_path = 'predictions.csv'
    
    output_df.to_csv(output_path, index=False, encoding='gbk')
```

3. **返回结构化结果**：
```python
# 返回结构化数据而不仅仅是文件
return {
    'predictions': predictions,
    'output_file': output_path,
    'total_files': len(predictions),
    'alert_count': sum(1 for p in predictions if p[2] == 1)
}
```

## 📊 总结

### predict()方法的核心特点：

1. **简单的输入格式**：标准CSV文件，不需要特殊文件夹结构
2. **灵活的特征维度**：自动适应9-11维特征
3. **固定的序列长度**：标准化到152个时间点
4. **独立的标准化**：每个文件独立进行标准化
5. **结构化输出**：文件名、风险分数、预测标签

### 融合系统设计要点：

1. **数据预处理**：确保CSV格式符合EarlysignalPred的要求
2. **路径管理**：修改硬编码路径，支持动态输入
3. **结果解析**：正确提取风险分数和预测标签
4. **错误处理**：处理文件格式不匹配等异常情况

这个分析为融合系统提供了准确的前驱信号检测集成方案。
