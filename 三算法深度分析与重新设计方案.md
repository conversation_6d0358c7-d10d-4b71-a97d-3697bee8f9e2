# 三种钻井检测算法深度分析与重新设计方案

## 📋 执行摘要

经过深入分析，发现现有统一检测系统存在根本性问题：**没有真正理解三个算法的数据要求、加载方式和输出格式**。本文档提供详细的算法分析和重新设计方案。

## 🔍 三算法深度分析

### 1. 前驱信号检测算法

#### 运行配置
```bash
python run.py \
  --task_name earlysignaldet \
  --is_training 0 \
  --root_path ./dataset2 \
  --model PatchTST \
  --data Earlysignaldet \
  --features M \
  --seq_len 96 \
  --enc_in 10 \
  --d_model 128 \
  --batch_size 1
```

#### 数据要求
- **格式**：CSV文件
- **特征维度**：10维（DEP, BITDEP, HOKHEI, DRITIME, WOB, HKLD, RPM, TOR, SPP, date）
- **文件夹结构**：必须有特定结构
  ```
  root_path/
  ├── normal/          # 正常数据（标签0）
  │   ├── 片段1.csv
  │   ├── 片段2.csv
  │   └── ...
  └── earlysignal2/    # 前驱信号数据（标签1）
      ├── 片段1.csv
      └── ...
  ```

#### 数据加载机制
- 使用`EarlysignaldetLoader`类
- 自动扫描normal/和earlysignal2/文件夹
- 使用`train_test_split`分割数据
- 根据flag参数（'TRAIN'/'TEST'）选择数据集

#### 输出格式
- 风险分数：0-1之间的浮点数
- 预测标签：0（正常）或1（预警）
- 支持3分钟片段级别的批量处理

### 2. 异常检测算法

#### 运行配置
```bash
python run.py \
  --task_name anomaly_detection \
  --is_training 0 \
  --root_path ./dataset/test \
  --model FEDformer \
  --data anomaly_detection \
  --seq_len 96 \
  --label_len 48 \
  --pred_len 96 \
  --enc_in 12 \
  --d_model 128 \
  --test_file 测试数据.npy \
  --no_label
```

#### 数据要求
- **格式**：NPY文件（不是CSV！）
- **特征维度**：12维（比标准多2维）
- **数据结构**：(n_samples, 13) - 12个特征 + 1个时间戳
- **训练数据**：需要train自201H54-3.npy进行标准化

#### 数据加载机制
- 使用`AnomalyDetectionLoader`类
- 加载训练数据进行StandardScaler拟合
- 对测试数据应用相同的标准化
- 去除最后一列时间戳，保留12维特征

#### 输出格式
- **关键输出**：经验卡钻检测异常比例（百分比）
- 通过正则表达式提取：`经验卡钻检测异常比例: (\d+\.\d+)%`
- 需要转换为0-1风险分数：`risk = anomaly_ratio / 100.0`

### 3. 专家经验算法

#### 运行配置
- **模型文件**：checkpoints/multiwell_model.pkl
- **窗口大小**：30个数据点
- **置信度阈值**：0.85
- **规则得分阈值**：4

#### 数据要求
- **格式**：CSV文件
- **必需列**：DEP, BITDEP, HOKHEI, HKLD, RPM, TOR, FLOWIN, FLOWOUT, SPP, date
- **重命名映射**：
  ```python
  {
      'DEP': 'Well_Depth',
      'BITDEP': 'Bit_Depth', 
      'HOKHEI': 'Hook_Height',
      'HKLD': 'Hookload',
      'RPM': 'RPM',
      'TOR': 'Torque',
      'FLOWIN': 'Flow_In',
      'FLOWOUT': 'Flow_Out',
      'SPP': 'Standpipe_Pressure'
  }
  ```

#### 处理流程
1. **滑动窗口特征计算**（窗口=30）：
   - depth_change = max - min
   - bit_change = max - min  
   - rpm_now = 当前值
   - flow_diff = (Flow_In - Flow_Out).mean()
   - hook_height_change = max - min
   - hookload_change = max - min
   - flow_rpm_pattern = 1 if (rpm_now < 5 and flow_diff > 15) else 0

2. **规则得分计算**（0-6分）：
   ```python
   score = 0
   if depth_change < 0.001: score += 1
   if bit_change < 0.001: score += 1  
   if rpm_now < 5: score += 1
   if hook_height_change < 0.01: score += 1
   if hookload_change > 3: score += 1
   if flow_rpm_pattern == 1: score += 1
   ```

3. **机器学习预测**：
   - 使用GradientBoostingClassifier
   - 输入6个特征，输出概率

4. **融合判断**：
   ```python
   final_label = 1 if (score >= 4 AND model_prob >= 0.85) else 0
   ```

#### 输出格式
- 规则得分：0-6的整数
- 模型概率：0-1的浮点数
- 最终标签：0或1
- 需要至少30个数据点才能开始计算

## ❌ 现有系统问题分析

### 1. 数据格式理解错误
- **问题**：假设所有算法都接受CSV输入
- **实际**：异常检测需要NPY格式，且维度不同

### 2. 特征维度处理不当
- **问题**：简单的0填充扩展维度
- **实际**：需要理解每个算法的特征语义

### 3. 片段处理策略不合理
- **问题**：强制所有算法使用3分钟片段
- **实际**：专家系统需要足够长的数据来计算窗口特征

### 4. 输出解析不准确
- **问题**：没有正确解析各算法的真实输出
- **实际**：需要针对性的输出解析策略

### 5. 文件夹结构要求忽略
- **问题**：没有为前驱信号检测创建正确的文件夹结构
- **实际**：EarlysignaldetLoader有严格的文件夹要求

## 🚀 重新设计方案

### 1. 统一数据预处理模块

```python
class UnifiedDataProcessor:
    def __init__(self):
        self.standard_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 
                                 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'date']
        
    def prepare_for_precursor(self, csv_file):
        """为前驱信号检测准备数据"""
        # 1. 读取CSV并对齐到10维标准特征
        # 2. 按3分钟分割
        # 3. 创建normal/和earlysignal2/文件夹结构
        # 4. 返回root_path和文件列表
        
    def prepare_for_anomaly(self, csv_file):
        """为异常检测准备数据"""
        # 1. 读取CSV并扩展到12维
        # 2. 转换为NPY格式
        # 3. 应用标准化（如果有训练数据）
        # 4. 返回NPY文件路径
        
    def prepare_for_expert(self, csv_file):
        """为专家系统准备数据"""
        # 1. 读取CSV并重命名列
        # 2. 添加流量列（如果缺失）
        # 3. 确保数据长度足够（至少30个点）
        # 4. 返回处理后的CSV文件
```

### 2. 智能算法适配器

```python
class PrecursorDetectionAdapter:
    def run_algorithm(self, csv_file):
        # 1. 调用数据预处理
        root_path, segment_files = self.processor.prepare_for_precursor(csv_file)
        
        # 2. 构建正确的命令行参数
        cmd = [
            "python", "前驱信号检测/run.py",
            "--task_name=earlysignaldet",
            "--is_training=0", 
            f"--root_path={root_path}",
            "--model=PatchTST",
            "--data=Earlysignaldet",
            "--features=M",
            "--seq_len=96",
            "--enc_in=10"
        ]
        
        # 3. 执行并解析输出
        # 4. 返回标准化结果

class AnomalyDetectionAdapter:
    def run_algorithm(self, csv_file):
        # 1. 转换为NPY格式
        npy_file = self.processor.prepare_for_anomaly(csv_file)
        
        # 2. 构建命令行参数
        cmd = [
            "python", "异常检测/run.py",
            "--task_name=anomaly_detection",
            "--is_training=0",
            f"--test_file={os.path.basename(npy_file)}",
            f"--root_path={os.path.dirname(npy_file)}",
            "--model=FEDformer",
            "--enc_in=12",
            "--no_label"
        ]
        
        # 3. 执行并提取异常比例
        # 4. 转换为标准化风险分数

class ExpertSystemAdapter:
    def run_algorithm(self, csv_file):
        # 1. 数据预处理和列重命名
        processed_file = self.processor.prepare_for_expert(csv_file)
        
        # 2. 直接调用专家规则计算
        # 3. 加载预训练模型进行预测
        # 4. 应用融合逻辑
        # 5. 返回标准化结果
```

### 3. 结果标准化与融合

```python
class ResultNormalizer:
    def normalize_precursor_result(self, result):
        """前驱信号检测结果已经是0-1风险分数"""
        return {
            'risk': result.get('risk', 0.0),
            'label': result.get('label', 0),
            'confidence': result.get('confidence', 0.5)
        }
    
    def normalize_anomaly_result(self, result):
        """异常检测结果转换"""
        anomaly_ratio = result.get('anomaly_ratio', 0.0)
        risk = anomaly_ratio / 100.0  # 百分比转换为0-1
        label = 1 if anomaly_ratio > 10.0 else 0  # 阈值判断
        
        return {
            'risk': risk,
            'label': label, 
            'confidence': min(risk * 2, 1.0)  # 简单的置信度计算
        }
    
    def normalize_expert_result(self, result):
        """专家系统结果转换"""
        score = result.get('score', 0)
        prob = result.get('probability', 0.0)
        
        # 规则得分标准化到0-1
        risk_from_rules = score / 6.0
        # 模型概率已经是0-1
        risk_from_model = prob
        
        # 加权融合
        risk = 0.6 * risk_from_model + 0.4 * risk_from_rules
        label = result.get('label', 0)
        
        return {
            'risk': risk,
            'label': label,
            'confidence': prob
        }

class AdvancedFusion:
    def __init__(self, weights={'precursor': 0.5, 'anomaly': 0.3, 'expert': 0.2}):
        self.weights = weights
        self.normalizer = ResultNormalizer()
    
    def fuse_results(self, precursor_result, anomaly_result, expert_result):
        # 1. 标准化所有结果
        norm_precursor = self.normalizer.normalize_precursor_result(precursor_result)
        norm_anomaly = self.normalizer.normalize_anomaly_result(anomaly_result)
        norm_expert = self.normalizer.normalize_expert_result(expert_result)
        
        # 2. 计算加权风险分数
        fused_risk = (
            self.weights['precursor'] * norm_precursor['risk'] +
            self.weights['anomaly'] * norm_anomaly['risk'] +
            self.weights['expert'] * norm_expert['risk']
        )
        
        # 3. 智能标签融合（考虑置信度）
        weighted_votes = (
            self.weights['precursor'] * norm_precursor['label'] * norm_precursor['confidence'] +
            self.weights['anomaly'] * norm_anomaly['label'] * norm_anomaly['confidence'] +
            self.weights['expert'] * norm_expert['label'] * norm_expert['confidence']
        )
        
        total_confidence = (
            self.weights['precursor'] * norm_precursor['confidence'] +
            self.weights['anomaly'] * norm_anomaly['confidence'] +
            self.weights['expert'] * norm_expert['confidence']
        )
        
        fused_label = 1 if (weighted_votes / total_confidence) > 0.5 else 0
        
        return {
            'risk': fused_risk,
            'label': fused_label,
            'confidence': total_confidence,
            'details': {
                'precursor': norm_precursor,
                'anomaly': norm_anomaly,
                'expert': norm_expert
            }
        }
```

## 📊 关键改进点

### 1. 真实理解算法需求
- 深入分析每个算法的数据加载机制
- 正确处理文件夹结构和格式要求
- 准确解析算法输出

### 2. 智能数据转换
- CSV ↔ NPY格式转换
- 10维 ↔ 12维特征对齐
- 流量特征的智能填充

### 3. 自适应片段策略
- 前驱信号：严格3分钟片段
- 异常检测：灵活片段长度
- 专家系统：确保足够数据点

### 4. 鲁棒融合机制
- 考虑算法置信度的加权融合
- 处理算法失败的容错机制
- 动态权重调整能力

## 🎯 实施建议

1. **分阶段实施**：先实现单个算法适配器，再进行融合
2. **充分测试**：使用真实数据验证每个算法的输出
3. **性能优化**：缓存机制和并行处理
4. **文档完善**：详细的API文档和使用示例

这个重新设计的方案将真正理解和尊重每个算法的特点，实现有效的融合检测系统。

## 🔧 技术实现细节

### 数据流转图

```
原始CSV文件
    ↓
统一数据预处理模块
    ├── 前驱信号路径: CSV → 10维对齐 → 3分钟分割 → 文件夹结构 → run.py
    ├── 异常检测路径: CSV → 12维扩展 → NPY转换 → 标准化 → run.py
    └── 专家系统路径: CSV → 列重命名 → 流量填充 → 窗口计算 → 内置处理
    ↓
算法适配器层
    ├── PrecursorAdapter: 解析风险分数和标签
    ├── AnomalyAdapter: 提取异常比例，转换为风险分数
    └── ExpertAdapter: 计算规则得分和模型概率
    ↓
结果标准化层
    ├── 统一风险分数格式 (0-1)
    ├── 统一标签格式 (0/1)
    └── 计算置信度
    ↓
智能融合层
    ├── 加权风险分数融合
    ├── 置信度加权标签融合
    └── 异常情况处理
    ↓
最终输出: {risk, label, confidence, details}
```

### 关键技术挑战与解决方案

#### 1. 特征维度对齐问题

**挑战**：
- 前驱信号检测：10维特征
- 异常检测：12维特征
- 专家系统：9维+流量特征

**解决方案**：
```python
class FeatureAligner:
    def align_for_anomaly_detection(self, data_10d):
        """10维 → 12维扩展策略"""
        # 方案1：使用相关特征的组合
        data_12d = data_10d.copy()
        data_12d['derived_feature_1'] = data_10d['WOB'] * data_10d['RPM']  # 钻压×转速
        data_12d['derived_feature_2'] = data_10d['HKLD'] - data_10d['WOB']  # 大钩负荷-钻压

        # 方案2：使用统计特征
        # data_12d['feature_11'] = data_10d[numeric_cols].std(axis=1)  # 横向标准差
        # data_12d['feature_12'] = data_10d[numeric_cols].mean(axis=1)  # 横向均值

        return data_12d

    def add_flow_features(self, data):
        """为专家系统添加流量特征"""
        if 'FLOWIN' not in data.columns:
            # 基于其他特征估算流量
            data['FLOWIN'] = 30.0 + data['SPP'] * 0.5  # 基于立压估算
        if 'FLOWOUT' not in data.columns:
            data['FLOWOUT'] = data['FLOWIN'] * 0.85  # 假设5%损失
        return data
```

#### 2. 时间窗口处理策略

**挑战**：不同算法对数据长度要求不同

**解决方案**：
```python
class AdaptiveSegmentation:
    def segment_for_algorithm(self, data, algorithm_type):
        if algorithm_type == 'precursor':
            # 严格3分钟分割
            return self.fixed_time_segments(data, minutes=3)
        elif algorithm_type == 'anomaly':
            # 灵活分割，最小96个点
            return self.adaptive_segments(data, min_points=96)
        elif algorithm_type == 'expert':
            # 确保足够窗口，重叠分割
            return self.overlapping_segments(data, window=30, overlap=15)

    def overlapping_segments(self, data, window, overlap):
        """重叠窗口分割，确保专家系统有足够数据"""
        segments = []
        step = window - overlap
        for i in range(0, len(data) - window + 1, step):
            segment = data.iloc[i:i+window]
            if len(segment) >= window:
                segments.append(segment)
        return segments
```

#### 3. 算法输出解析

**挑战**：每个算法的输出格式完全不同

**解决方案**：
```python
class OutputParser:
    def parse_precursor_output(self, stdout_text):
        """解析前驱信号检测输出"""
        patterns = {
            'risk': r'risk[:\s]+([0-9.]+)',
            'label': r'label[:\s]+([01])',
            'accuracy': r'accuracy[:\s]+([0-9.]+)'
        }

        result = {}
        for key, pattern in patterns.items():
            match = re.search(pattern, stdout_text, re.IGNORECASE)
            if match:
                result[key] = float(match.group(1))

        return result

    def parse_anomaly_output(self, stdout_text):
        """解析异常检测输出"""
        # 关键：提取经验卡钻检测异常比例
        ratio_match = re.search(r'经验卡钻检测异常比例: (\d+\.\d+)%', stdout_text)
        if ratio_match:
            anomaly_ratio = float(ratio_match.group(1))
            return {
                'anomaly_ratio': anomaly_ratio,
                'risk': anomaly_ratio / 100.0,
                'label': 1 if anomaly_ratio > 10.0 else 0
            }
        return {'anomaly_ratio': 0.0, 'risk': 0.0, 'label': 0}

    def parse_expert_output(self, features_df, model_predictions):
        """解析专家系统输出"""
        results = []
        for i, (_, row) in enumerate(features_df.iterrows()):
            score = row['stuck_score']
            prob = model_predictions[i]
            label = 1 if (score >= 4 and prob >= 0.85) else 0

            results.append({
                'score': score,
                'probability': prob,
                'label': label,
                'risk': 0.6 * prob + 0.4 * (score / 6.0)
            })
        return results
```

### 错误处理与容错机制

```python
class RobustAlgorithmRunner:
    def run_with_fallback(self, algorithm_type, data_file):
        """带容错的算法运行"""
        try:
            if algorithm_type == 'precursor':
                return self.run_precursor(data_file)
        except Exception as e:
            logger.warning(f"前驱信号检测失败: {e}")
            return self.get_default_result('precursor')

        # 类似的容错逻辑...

    def get_default_result(self, algorithm_type):
        """算法失败时的默认结果"""
        defaults = {
            'precursor': {'risk': 0.3, 'label': 0, 'confidence': 0.1},
            'anomaly': {'risk': 0.2, 'label': 0, 'confidence': 0.1},
            'expert': {'risk': 0.1, 'label': 0, 'confidence': 0.1}
        }
        return defaults.get(algorithm_type, {'risk': 0.0, 'label': 0, 'confidence': 0.0})
```

## 📈 性能优化策略

### 1. 缓存机制
- 训练数据标准化结果缓存
- 模型加载缓存
- 中间处理结果缓存

### 2. 并行处理
- 三个算法可以并行运行
- 批量文件的并行处理
- 片段级别的并行计算

### 3. 内存优化
- 大文件分块处理
- 及时清理临时文件
- 流式数据处理

## 🧪 验证与测试策略

### 1. 单元测试
- 每个算法适配器的独立测试
- 数据转换模块的正确性验证
- 输出解析的准确性测试

### 2. 集成测试
- 端到端的融合流程测试
- 不同数据格式的兼容性测试
- 异常情况的容错测试

### 3. 性能测试
- 大批量数据的处理能力
- 内存使用情况监控
- 算法运行时间分析

## 🎯 部署与维护

### 1. 配置管理
- 算法参数的集中配置
- 权重的动态调整机制
- 阈值的自适应优化

### 2. 监控与日志
- 详细的运行日志记录
- 算法性能指标监控
- 异常情况的告警机制

### 3. 版本管理
- 算法模型的版本控制
- 配置文件的版本管理
- 向后兼容性保证

这个重新设计的方案解决了现有系统的根本问题，真正理解了三个算法的特点，并提供了完整的技术实现路径。
