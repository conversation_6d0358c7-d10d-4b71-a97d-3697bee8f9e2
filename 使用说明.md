# 统一钻井检测系统使用说明

## 🎯 系统概述

统一钻井检测系统融合了三个钻井算法：
- **前驱信号检测** (权重 0.5)
- **异常检测** (权重 0.3) 
- **专家经验系统** (权重 0.2)

系统完全兼容前驱信号检测的3分钟片段处理方式，输出格式为 `[Filename, Risk, Predicted_Label]`。

## 🚀 快速开始

### 方法1：直接运行（推荐）
```bash
python unified_detection_system.py
```
系统会自动进入演示模式，引导您选择文件和处理方式。

### 方法2：命令行模式
```bash
# 单文件检测
python unified_detection_system.py data.csv

# 批量检测
python unified_detection_system.py --batch input_directory/

# 指定输出文件
python unified_detection_system.py --batch input_directory/ -o results.csv
```

## 📁 输入数据格式

系统支持CSV格式，需要包含以下列（缺失列会自动填充）：
- `DEP` - 井深
- `BITDEP` - 钻头深度  
- `HOKHEI` - 大钩高度
- `DRITIME` - 钻进时间
- `WOB` - 钻压
- `HKLD` - 大钩载荷
- `RPM` - 转速
- `TOR` - 扭矩
- `SPP` - 立管压力
- `date` - 时间戳（可选）

## 📊 输出格式

系统输出CSV文件，包含三列：
- `Filename` - 片段文件名（格式：井名_开始时间-结束时间.csv）
- `Risk` - 风险分数（0-1之间）
- `Predicted_Label` - 预测标签（0=正常，1=预警）

## 🔧 系统验证

运行快速验证脚本检查系统状态：
```bash
python quick_test.py
```

## ⚙️ 高级配置

### 自定义权重
```bash
python unified_detection_system.py data.csv --precursor-weight 0.6 --anomaly-weight 0.2 --expert-weight 0.2
```

### 输出模式选择
```bash
# 片段级别输出（默认，兼容前驱信号检测）
python unified_detection_system.py --batch input_dir/ --segment-output

# 文件级别输出
python unified_detection_system.py --batch input_dir/ --file-output
```

## 🔍 工作原理

1. **数据分割**：将原始数据按3分钟分割成片段
2. **并行处理**：三个算法同时处理所有片段
3. **结果融合**：按权重融合三个算法的结果
4. **输出生成**：生成兼容前驱信号检测格式的结果

## 📝 注意事项

- 确保输入数据包含时间列或足够的数据点
- 系统会自动处理缺失特征（用0填充）
- 临时文件会自动清理
- 支持中文路径和文件名

## 🛠️ 故障排除

### 常见问题

1. **找不到CSV文件**
   - 确保文件在当前目录或指定正确路径
   - 检查文件扩展名是否为.csv

2. **数据格式错误**
   - 检查CSV文件编码（建议UTF-8）
   - 确保数据包含数值列

3. **算法执行失败**
   - 运行 `python quick_test.py` 检查系统状态
   - 查看日志信息定位问题

### 获取详细日志
```bash
python unified_detection_system.py data.csv -v
```

## 📞 技术支持

如遇问题，请提供：
- 错误信息截图
- 输入数据样例
- 运行环境信息
