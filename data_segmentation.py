#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分割模块
将原始钻井数据按时间段分割，兼容前驱信号检测的3分钟片段处理方式

作者：AI Assistant
创建时间：2025-01-27
"""

import os
import pandas as pd
import numpy as np
import tempfile
import shutil
from typing import List, Tuple, Dict, Optional
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class DataSegmentation:
    """数据分割器"""
    
    def __init__(self, segment_duration_minutes: int = 3):
        """
        初始化数据分割器
        
        Args:
            segment_duration_minutes: 分割时间段长度（分钟）
        """
        self.segment_duration = pd.Timedelta(minutes=segment_duration_minutes)
        self.temp_dirs = []  # 记录临时目录，用于清理
        
    def segment_data(self, csv_file: str, output_dir: str = None) -> Tuple[str, List[str]]:
        """
        将CSV文件按时间段分割
        
        Args:
            csv_file: 输入CSV文件路径
            output_dir: 输出目录，如果为None则创建临时目录
            
        Returns:
            (输出目录路径, 分割文件列表)
        """
        logger.info(f"开始分割数据文件: {csv_file}")
        
        # 创建输出目录
        if output_dir is None:
            output_dir = tempfile.mkdtemp(prefix="segments_")
            self.temp_dirs.append(output_dir)
            logger.debug(f"创建临时目录: {output_dir}")
        else:
            os.makedirs(output_dir, exist_ok=True)
        
        # 读取数据
        try:
            df = pd.read_csv(csv_file)
            logger.info(f"读取数据成功，形状: {df.shape}")
        except Exception as e:
            logger.error(f"读取数据失败: {str(e)}")
            raise
        
        # 确保有时间列
        if 'date' not in df.columns:
            logger.warning("未找到时间列，使用行索引创建时间序列")
            # 创建假的时间序列，每5分钟一个数据点
            start_time = datetime.now()
            df['date'] = [start_time + timedelta(minutes=5*i) for i in range(len(df))]
        
        # 转换时间列
        try:
            df['date'] = pd.to_datetime(df['date'])
        except Exception as e:
            logger.error(f"时间列转换失败: {str(e)}")
            raise
        
        # 按时间排序
        df = df.sort_values(by='date')
        
        # 获取井名（从文件名提取）
        well_name = os.path.splitext(os.path.basename(csv_file))[0]
        
        # 开始分割
        segment_files = []
        start_time = df['date'].min()
        end_time = df['date'].max()
        current_start = start_time
        segment_count = 0
        
        logger.info(f"数据时间范围: {start_time} 到 {end_time}")
        
        while current_start <= end_time:
            current_end = current_start + self.segment_duration
            
            # 提取当前时间段的数据
            segment_df = df[(df['date'] >= current_start) & (df['date'] < current_end)]
            
            if not segment_df.empty:
                # 格式化时间字符串
                start_time_str = current_start.strftime("%Y-%m-%d_%H-%M-%S")
                end_time_str = current_end.strftime("%Y-%m-%d_%H-%M-%S")
                
                # 生成分割文件名（兼容前驱信号检测的命名格式）
                segment_filename = f"{well_name}_{start_time_str}-{end_time_str}.csv"
                segment_filepath = os.path.join(output_dir, segment_filename)
                
                # 保存分割文件
                segment_df.to_csv(segment_filepath, index=False)
                segment_files.append(segment_filepath)
                segment_count += 1
                
                logger.debug(f"创建分割文件: {segment_filename}, 数据点: {len(segment_df)}")
            
            current_start = current_end
        
        logger.info(f"数据分割完成，共生成 {segment_count} 个片段")
        return output_dir, segment_files
    
    def segment_for_algorithm(self, csv_file: str, algorithm_type: str) -> Tuple[str, List[str]]:
        """
        为特定算法分割数据

        Args:
            csv_file: 输入CSV文件
            algorithm_type: 算法类型 ('precursor', 'anomaly', 'expert')

        Returns:
            (输出目录, 分割文件列表)
        """
        if algorithm_type == 'precursor':
            # 前驱信号检测需要特殊的文件夹结构
            return self.segment_data_for_precursor(csv_file)
        elif algorithm_type == 'anomaly':
            # 异常检测可以使用更长的片段或整个文件
            return self.segment_data(csv_file)  # 暂时也用3分钟片段
        elif algorithm_type == 'expert':
            # 专家系统需要足够长的数据来计算窗口特征
            return self.segment_data(csv_file)  # 暂时也用3分钟片段
        else:
            raise ValueError(f"不支持的算法类型: {algorithm_type}")

    def segment_data_for_precursor(self, csv_file: str, output_dir: str = None) -> Tuple[str, List[str]]:
        """
        为前驱信号检测分割数据，创建EarlysignaldetLoader期望的文件夹结构

        Args:
            csv_file: 输入CSV文件路径
            output_dir: 输出目录，如果为None则创建临时目录

        Returns:
            (输出目录路径, 分割文件列表)
        """
        logger.info(f"为前驱信号检测分割数据文件: {csv_file}")

        # 创建输出目录
        if output_dir is None:
            output_dir = tempfile.mkdtemp(prefix="precursor_segments_")
            self.temp_dirs.append(output_dir)
            logger.debug(f"创建临时目录: {output_dir}")
        else:
            os.makedirs(output_dir, exist_ok=True)

        # 创建EarlysignaldetLoader期望的子文件夹结构
        # 注意：这里我们将测试数据放入normal文件夹，因为我们不知道真实标签
        normal_dir = os.path.join(output_dir, 'normal')
        earlysignal_dir = os.path.join(output_dir, 'earlysignal2')
        os.makedirs(normal_dir, exist_ok=True)
        os.makedirs(earlysignal_dir, exist_ok=True)

        # 先进行常规分割
        temp_dir, segment_files = self.segment_data(csv_file)

        # 将分割文件移动到normal文件夹（用于测试）
        moved_files = []
        for segment_file in segment_files:
            filename = os.path.basename(segment_file)
            target_path = os.path.join(normal_dir, filename)
            shutil.move(segment_file, target_path)
            moved_files.append(target_path)

        # 清理临时目录
        if temp_dir in self.temp_dirs:
            self.temp_dirs.remove(temp_dir)
        shutil.rmtree(temp_dir, ignore_errors=True)

        logger.info(f"前驱信号检测数据分割完成，共生成 {len(moved_files)} 个片段")
        return output_dir, moved_files
    
    def merge_segment_results(self, segment_results: List[Dict], algorithm_type: str) -> Dict:
        """
        合并分割片段的检测结果
        
        Args:
            segment_results: 片段检测结果列表
            algorithm_type: 算法类型
            
        Returns:
            合并后的结果
        """
        if not segment_results:
            return {"risk": 0.0, "label": 0}
        
        # 提取有效结果
        valid_results = [r for r in segment_results if r.get('success', True)]
        
        if not valid_results:
            logger.warning(f"{algorithm_type}算法所有片段都失败")
            return {"risk": 0.0, "label": 0}
        
        # 计算平均风险分数
        risks = [r.get('risk', 0.0) for r in valid_results]
        avg_risk = np.mean(risks)
        
        # 计算标签（多数投票）
        labels = [r.get('label', 0) for r in valid_results]
        label_counts = {0: labels.count(0), 1: labels.count(1)}
        merged_label = 1 if label_counts[1] > label_counts[0] else 0
        
        # 计算置信度（预警片段比例）
        confidence = label_counts[1] / len(labels) if labels else 0.0
        
        logger.info(f"{algorithm_type}算法片段合并: {len(valid_results)}个片段, "
                   f"平均风险={avg_risk:.3f}, 预警片段={label_counts[1]}/{len(labels)}")
        
        return {
            "risk": avg_risk,
            "label": merged_label,
            "confidence": confidence,
            "segment_count": len(valid_results),
            "alert_segments": label_counts[1],
            "segment_results": valid_results
        }
    
    def create_segment_summary(self, segment_results: List[Dict], original_filename: str) -> pd.DataFrame:
        """
        创建片段检测结果汇总
        
        Args:
            segment_results: 片段检测结果列表
            original_filename: 原始文件名
            
        Returns:
            汇总DataFrame
        """
        summary_data = []
        
        for result in segment_results:
            if result.get('success', True):
                # 从文件名解析时间信息
                filename = result.get('filename', '')
                
                summary_data.append({
                    'Original_File': original_filename,
                    'Segment_File': filename,
                    'Risk': result.get('risk', 0.0),
                    'Predicted_Label': result.get('label', 0),
                    'Algorithm_Details': result.get('details', {})
                })
        
        return pd.DataFrame(summary_data)
    
    def cleanup_temp_dirs(self):
        """清理临时目录"""
        for temp_dir in self.temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.debug(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录失败 {temp_dir}: {str(e)}")
        
        self.temp_dirs.clear()
    
    def __del__(self):
        """析构函数，自动清理临时目录"""
        self.cleanup_temp_dirs()


class SegmentResultProcessor:
    """片段结果处理器"""
    
    @staticmethod
    def extract_time_from_filename(filename: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        从文件名提取时间信息
        
        Args:
            filename: 文件名，格式如 "井名_2025-01-27_10-30-00-2025-01-27_10-33-00.csv"
            
        Returns:
            (开始时间, 结束时间)
        """
        import re
        
        # 正则表达式匹配时间格式
        pattern = r'(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})-(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})'
        match = re.search(pattern, filename)
        
        if match:
            try:
                start_str, end_str = match.groups()
                start_time = datetime.strptime(start_str, '%Y-%m-%d_%H-%M-%S')
                end_time = datetime.strptime(end_str, '%Y-%m-%d_%H-%M-%S')
                return start_time, end_time
            except ValueError:
                pass
        
        return None, None
    
    @staticmethod
    def merge_continuous_alerts(segment_results: List[Dict], time_tolerance_minutes: int = 1) -> List[Dict]:
        """
        合并连续的预警时间段
        
        Args:
            segment_results: 片段结果列表
            time_tolerance_minutes: 时间容差（分钟）
            
        Returns:
            合并后的预警时间段列表
        """
        # 筛选预警片段
        alert_segments = [r for r in segment_results if r.get('label', 0) == 1]
        
        if not alert_segments:
            return []
        
        # 提取时间信息并排序
        time_segments = []
        for segment in alert_segments:
            filename = segment.get('filename', '')
            start_time, end_time = SegmentResultProcessor.extract_time_from_filename(filename)
            if start_time and end_time:
                time_segments.append({
                    'start': start_time,
                    'end': end_time,
                    'data': segment
                })
        
        if not time_segments:
            return alert_segments  # 如果无法解析时间，返回原始结果
        
        # 按开始时间排序
        time_segments.sort(key=lambda x: x['start'])
        
        # 合并连续时间段
        merged_periods = []
        current_period = {
            'start': time_segments[0]['start'],
            'end': time_segments[0]['end'],
            'segments': [time_segments[0]['data']]
        }
        
        tolerance = timedelta(minutes=time_tolerance_minutes)
        
        for segment in time_segments[1:]:
            # 检查是否连续
            if abs(segment['start'] - current_period['end']) <= tolerance:
                # 连续，扩展当前时间段
                current_period['end'] = segment['end']
                current_period['segments'].append(segment['data'])
            else:
                # 不连续，保存当前时间段并开始新的时间段
                merged_periods.append(current_period)
                current_period = {
                    'start': segment['start'],
                    'end': segment['end'],
                    'segments': [segment['data']]
                }
        
        # 添加最后一个时间段
        merged_periods.append(current_period)
        
        return merged_periods
