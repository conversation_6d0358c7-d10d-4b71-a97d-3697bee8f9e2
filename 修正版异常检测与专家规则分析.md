# 修正版异常检测与专家规则分析

## 📋 概述

基于用户要求，本文档重新分析异常检测算法（去除经验卡钻部分，只保留重建误差阈值方法）和专家经验中的规则部分（不包括机器学习模型），确保融合算法正确运行。

## 🔍 异常检测算法修正分析

### 1. 简化的异常检测流程

#### 核心检测机制（仅重建误差阈值）
```python
def simplified_anomaly_detection(test_data, train_data, model, anomaly_ratio=1.0):
    """简化的异常检测：仅使用重建误差阈值方法"""
    
    # 1. 训练集重建误差计算
    model.eval()
    anomaly_criterion = nn.MSELoss(reduce=False)
    train_energy = []
    
    with torch.no_grad():
        for batch_x in train_loader:
            batch_x = batch_x.float().to(device)
            outputs = model(batch_x, None, None, None)
            score = torch.mean(anomaly_criterion(batch_x, outputs), dim=-1)
            train_energy.append(score.detach().cpu().numpy())
    
    train_energy = np.concatenate(train_energy, axis=0).reshape(-1)
    
    # 2. 测试集重建误差计算
    test_energy = []
    
    with torch.no_grad():
        for batch_x in test_loader:
            batch_x = batch_x.float().to(device)
            outputs = model(batch_x, None, None, None)
            score = torch.mean(anomaly_criterion(batch_x, outputs), dim=-1)
            test_energy.append(score.detach().cpu().numpy())
    
    test_energy = np.concatenate(test_energy, axis=0).reshape(-1)
    
    # 3. 阈值计算
    combined_energy = np.concatenate([train_energy, test_energy], axis=0)
    threshold = np.percentile(combined_energy, 100 - anomaly_ratio)
    
    # 4. 异常检测
    predictions = (test_energy > threshold).astype(int)
    anomaly_ratio_result = np.mean(predictions) * 100  # 转换为百分比
    
    return {
        'anomaly_ratio': anomaly_ratio_result,
        'predictions': predictions,
        'test_energy': test_energy,
        'threshold': threshold
    }
```

#### 修正的输出格式
```python
# 简化的输出信息
print(f"重建误差阈值: {threshold:.4f}")
print(f"异常检测比例: {anomaly_ratio_result:.2f}%")
print("使用重建误差阈值方法")
```

### 2. 支持文件夹批量处理的修改

#### 修改Ning209H83Loader支持多文件
```python
class Ning209H83LoaderBatch(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train", test_files=None, no_label=False):
        """
        支持批量文件处理的数据加载器
        
        Args:
            test_files: 测试文件列表，如 ['file1.npy', 'file2.npy', ...]
        """
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        self.no_label = no_label
        
        # 1. 加载训练数据进行标准化
        train_data = np.load(os.path.join(root_path, "train自201H54-3.npy"), allow_pickle=True)
        train_data = train_data[:, :-1]  # 去除时间戳
        self.scaler.fit(train_data)
        self.train = self.scaler.transform(train_data)
        
        # 2. 批量加载测试文件
        self.test_data_list = []
        self.test_labels_list = []
        self.test_timestamps_list = []
        self.file_names = []
        
        if test_files is None:
            # 自动扫描NPY文件
            test_files = [f for f in os.listdir(root_path) if f.endswith('.npy') and f != "train自201H54-3.npy"]
        
        for test_file in test_files:
            try:
                test_data_path = os.path.join(root_path, test_file)
                test_data = np.load(test_data_path, allow_pickle=True)
                
                # 提取时间戳和特征
                timestamp = test_data[:, -1]
                test_features = test_data[:, :-1]
                
                # 应用标准化
                test_normalized = self.scaler.transform(test_features)
                
                self.test_data_list.append(test_normalized)
                self.test_timestamps_list.append(timestamp)
                self.file_names.append(test_file)
                
                # 标签处理
                if not no_label:
                    label_file = f"{os.path.splitext(test_file)[0]}_label.npy"
                    label_path = os.path.join(root_path, label_file)
                    if os.path.exists(label_path):
                        labels = np.load(label_path, allow_pickle=True)
                        self.test_labels_list.append(labels)
                    else:
                        self.test_labels_list.append(np.zeros(len(test_data)))
                else:
                    self.test_labels_list.append(np.zeros(len(test_data)))
                    
                print(f"成功加载: {test_file}, 形状: {test_data.shape}")
                
            except Exception as e:
                print(f"加载文件失败 {test_file}: {e}")
        
        # 3. 合并所有测试数据
        if self.test_data_list:
            self.test = np.concatenate(self.test_data_list, axis=0)
            self.test_labels = np.concatenate(self.test_labels_list, axis=0)
            self.test_timestamps = np.concatenate(self.test_timestamps_list, axis=0)
        else:
            self.test = np.zeros((0, train_data.shape[1]))
            self.test_labels = np.zeros(0)
            self.test_timestamps = np.zeros(0)
```

#### 批量处理接口
```python
def batch_anomaly_detection(root_path, test_files=None):
    """批量异常检测接口"""
    
    # 1. 创建批量数据加载器
    test_data, test_loader = create_batch_data_loader(root_path, test_files)
    train_data, train_loader = create_batch_data_loader(root_path, None, flag='train')
    
    # 2. 加载模型
    model = load_pretrained_model()
    
    # 3. 执行简化的异常检测
    result = simplified_anomaly_detection(test_data, train_data, model)
    
    # 4. 按文件分割结果
    file_results = split_results_by_file(result, test_data.file_names, test_data.test_data_list)
    
    return file_results
```

## 🎯 专家规则算法分析

### 1. 核心规则提取

#### 标准规则集（基于run6_test.py）
```python
class ExpertRulesEngine:
    def __init__(self, window_size=30):
        self.window_size = window_size
        self.rules_config = {
            'depth_change_threshold': 0.001,
            'bit_change_threshold': 0.001,
            'rpm_threshold': 5,
            'hook_height_change_threshold': 0.01,
            'hookload_change_threshold': 3,
            'flow_diff_threshold': 15
        }
    
    def calculate_features(self, df, i):
        """计算单个时间点的特征"""
        w = df.iloc[i - self.window_size:i]  # 窗口数据
        row = df.iloc[i]                     # 当前行
        
        # 6个核心特征计算
        features = {
            'depth_change': w["Well_Depth"].max() - w["Well_Depth"].min(),
            'bit_change': w["Bit_Depth"].max() - w["Bit_Depth"].min(),
            'rpm_now': row["RPM"],
            'flow_diff': (w["Flow_In"] - w["Flow_Out"]).mean(),
            'hook_height_change': w["Hook_Height"].max() - w["Hook_Height"].min(),
            'hookload_change': w["Hookload"].max() - w["Hookload"].min()
        }
        
        # 复合特征
        features['flow_rpm_pattern'] = 1 if (features['rpm_now'] < self.rules_config['rpm_threshold'] 
                                           and features['flow_diff'] > self.rules_config['flow_diff_threshold']) else 0
        
        return features
    
    def calculate_rule_score(self, features):
        """计算规则得分（0-6分）"""
        score = 0
        
        # 规则1：井深变化小
        if features['depth_change'] < self.rules_config['depth_change_threshold']:
            score += 1
        
        # 规则2：钻头深度变化小
        if features['bit_change'] < self.rules_config['bit_change_threshold']:
            score += 1
        
        # 规则3：转速低
        if features['rpm_now'] < self.rules_config['rpm_threshold']:
            score += 1
        
        # 规则4：大钩高度变化小
        if features['hook_height_change'] < self.rules_config['hook_height_change_threshold']:
            score += 1
        
        # 规则5：大钩负荷变化大
        if features['hookload_change'] > self.rules_config['hookload_change_threshold']:
            score += 1
        
        # 规则6：流量-转速模式
        if features['flow_rpm_pattern'] == 1:
            score += 1
        
        return score
    
    def process_data(self, df):
        """处理整个数据文件"""
        # 数据预处理和列重命名
        df_processed = self.preprocess_data(df)
        
        results = []
        for i in range(self.window_size, len(df_processed)):
            # 计算特征
            features = self.calculate_features(df_processed, i)
            
            # 计算规则得分
            score = self.calculate_rule_score(features)
            
            # 保存结果
            result = {
                'timestamp': df_processed.iloc[i].get('date', i),
                'features': features,
                'rule_score': score,
                'risk': score / 6.0,  # 标准化到0-1
                'label': 1 if score >= 4 else 0  # 阈值判断
            }
            results.append(result)
        
        return results
    
    def preprocess_data(self, df):
        """数据预处理和列重命名"""
        # 列重命名映射
        column_mapping = {
            'DEP': 'Well_Depth',
            'BITDEP': 'Bit_Depth',
            'HOKHEI': 'Hook_Height',
            'HKLD': 'Hookload',
            'RPM': 'RPM',
            'TOR': 'Torque',
            'FLOWIN': 'Flow_In',
            'FLOWOUT': 'Flow_Out',
            'SPP': 'Standpipe_Pressure'
        }
        
        df_renamed = df.rename(columns=column_mapping)
        
        # 处理缺失的流量列
        if 'Flow_In' not in df_renamed.columns:
            df_renamed['Flow_In'] = 30.0  # 默认值
        if 'Flow_Out' not in df_renamed.columns:
            df_renamed['Flow_Out'] = 25.0  # 默认值
        
        return df_renamed
```

### 2. 规则变体分析

#### 不同版本的规则对比
```python
# run6版本（最终版本）- 6个规则
rules_v6 = {
    'depth_change < 0.001': 1,
    'bit_change < 0.001': 1,
    'rpm_now < 5': 1,
    'hook_height_change < 0.01': 1,
    'hookload_change > 3': 1,
    'flow_rpm_pattern == 1': 1  # rpm<5 AND flow_diff>15
}

# run5版本 - 4个规则（简化版）
rules_v5 = {
    'depth_change < 0.001': 1,
    'bit_change < 0.001': 1,
    'rpm_now < 5': 1,
    'flow_diff > 20': 1
}

# run2版本 - 7个规则（扩展版）
rules_v2 = {
    'depth_change < 0.01': 1,
    'bit_change < 0.01': 1,
    'rpm_now < 5': 1,
    'rpm_spike > 20': 1,
    'torque_spike > 5': 1,
    'flow_diff > 2': 1,
    'spp_change > 1': 1
}
```

### 3. 融合系统专家规则接口

#### 统一的专家规则适配器
```python
class ExpertRulesAdapter:
    def __init__(self, version='v6', window_size=30, score_threshold=4):
        self.version = version
        self.window_size = window_size
        self.score_threshold = score_threshold
        self.engine = ExpertRulesEngine(window_size)
    
    def predict_single_file(self, csv_file):
        """预测单个CSV文件"""
        try:
            # 1. 读取数据
            df = pd.read_csv(csv_file)
            
            # 2. 检查数据长度
            if len(df) < self.window_size:
                return {
                    'success': False,
                    'error': f'数据长度不足，需要至少{self.window_size}个数据点',
                    'risk': 0.0,
                    'label': 0
                }
            
            # 3. 执行规则计算
            results = self.engine.process_data(df)
            
            # 4. 汇总结果
            if not results:
                return {'success': False, 'error': '无有效结果', 'risk': 0.0, 'label': 0}
            
            scores = [r['rule_score'] for r in results]
            risks = [r['risk'] for r in results]
            labels = [r['label'] for r in results]
            
            # 5. 计算汇总指标
            avg_score = np.mean(scores)
            max_score = max(scores)
            avg_risk = np.mean(risks)
            final_label = 1 if max_score >= self.score_threshold else 0
            
            return {
                'success': True,
                'risk': avg_risk,
                'label': final_label,
                'avg_score': avg_score,
                'max_score': max_score,
                'total_points': len(results),
                'alert_points': sum(labels),
                'details': results
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'risk': 0.0,
                'label': 0
            }
    
    def predict_batch_files(self, csv_files):
        """批量预测多个CSV文件"""
        results = {}
        for csv_file in csv_files:
            file_name = os.path.basename(csv_file)
            results[file_name] = self.predict_single_file(csv_file)
        return results
```

## 📊 融合系统集成方案

### 1. 三算法统一接口

```python
class UnifiedAlgorithmInterface:
    def __init__(self):
        self.precursor_adapter = PrecursorDetectionAdapter()
        self.anomaly_adapter = SimplifiedAnomalyDetectionAdapter()
        self.expert_adapter = ExpertRulesAdapter()
    
    def predict_single_file(self, csv_file):
        """统一的单文件预测接口"""
        results = {}
        
        # 1. 前驱信号检测
        try:
            results['precursor'] = self.precursor_adapter.predict_single_file(csv_file)
        except Exception as e:
            results['precursor'] = {'success': False, 'error': str(e), 'risk': 0.0, 'label': 0}
        
        # 2. 异常检测（简化版）
        try:
            results['anomaly'] = self.anomaly_adapter.predict_single_file(csv_file)
        except Exception as e:
            results['anomaly'] = {'success': False, 'error': str(e), 'risk': 0.0, 'label': 0}
        
        # 3. 专家规则
        try:
            results['expert'] = self.expert_adapter.predict_single_file(csv_file)
        except Exception as e:
            results['expert'] = {'success': False, 'error': str(e), 'risk': 0.0, 'label': 0}
        
        return results
```

### 2. 简化的输出解析

```python
def parse_simplified_anomaly_output(stdout_text):
    """解析简化的异常检测输出"""
    result = {'success': False}
    
    # 提取异常检测比例
    ratio_match = re.search(r'异常检测比例:\s*(\d+\.\d+)%', stdout_text)
    if ratio_match:
        anomaly_ratio = float(ratio_match.group(1))
        result['anomaly_ratio'] = anomaly_ratio
        result['risk'] = anomaly_ratio / 100.0
        result['label'] = 1 if anomaly_ratio > 10.0 else 0
        result['method'] = 'threshold_detection'
        result['success'] = True
    
    return result
```

## 🎯 总结

### 修正后的关键特点：

1. **异常检测简化**：只使用重建误差阈值方法，去除经验卡钻检测
2. **支持批量处理**：修改数据加载器支持文件夹级别的批量处理
3. **专家规则提取**：提取纯规则计算部分，不依赖机器学习模型
4. **统一接口设计**：三个算法使用统一的预测接口
5. **简化输出解析**：减少复杂的输出解析逻辑

### 融合系统优势：

1. **降低复杂度**：去除不稳定的经验卡钻检测
2. **提高可靠性**：专家规则不依赖外部模型文件
3. **增强扩展性**：支持批量文件处理
4. **统一标准化**：所有算法输出标准化的风险分数和标签

这个修正方案确保了融合系统的稳定性和可靠性。
