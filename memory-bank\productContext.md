# Product Context

## Project Overview
统一钻井检测系统 - 融合三种钻井卡钻检测算法的综合检测平台：
1. 前驱信号检测算法（基于PatchTST，权重0.5）
2. 异常检测算法（基于FEDformer，权重0.3）
3. 专家经验规则算法（规则引擎+机器学习，权重0.2）

目标：提升钻井卡钻检测的准确性和可靠性，完全兼容前驱信号检测的3分钟片段处理方式。

## Project Goal
融合前驱信号检测、异常检测、专家经验三个算法，构建统一的钻井卡钻检测系统，实现：
- 算法优势互补，提升检测精度
- 保持与现有前驱信号检测系统的完全兼容性
- 统一的输入输出接口，便于集成和使用

## Key Features
- ✅ 三算法统一融合：前驱信号检测 + 异常检测 + 专家经验
- ✅ 3分钟片段处理：完全兼容前驱信号检测格式
- ✅ 统一输入输出：标准10维特征，CSV格式输入输出
- ✅ 权重融合策略：可配置的算法权重（默认0.5+0.3+0.2）
- ✅ 批量处理能力：支持单文件和批量文件处理
- ✅ 适配器模式：易于扩展和维护的模块化设计
- ✅ V2.0完整代码实现：基于修正版分析的完整可运行代码实现，包含简化异常检测、纯规则专家系统、统一融合引擎和批量处理能力
- ✅ V2.0部署和测试指南：完整的系统部署、配置、测试和故障排除指南，确保用户能够独立完成系统部署和验证

## Overall Architecture

### V2.0 重新设计架构（2025-07-27）
```
┌─────────────────────────────────────────────────────────────┐
│                    统一检测系统 V2.0                        │
├─────────────────────────────────────────────────────────────┤
│  输入层：CSV/NPY文件 → 批量文件夹 → 数据流                  │
├─────────────────────────────────────────────────────────────┤
│  数据预处理层                                               │
│  ├── CSV ↔ NPY 转换器                                      │
│  ├── 特征对齐与标准化                                       │
│  └── 文件夹结构管理器                                       │
├─────────────────────────────────────────────────────────────┤
│  算法适配层（重新设计）                                     │
│  ├── 简化异常检测适配器    ├── 纯规则专家适配器             │
│  └── 前驱信号检测适配器                                     │
├─────────────────────────────────────────────────────────────┤
│  融合引擎层                                                 │
│  ├── 统一接口管理器        ├── 权重融合计算器               │
│  └── 结果标准化处理器                                       │
├─────────────────────────────────────────────────────────────┤
│  输出层：统一风险分数 + 预测标签 + 详细报告                 │
└─────────────────────────────────────────────────────────────┘
```

### V2.0 核心组件：
- **DataProcessorV2**: 重新设计的数据预处理器（CSV↔NPY转换、特征对齐）
- **SimplifiedAnomalyDetectionAdapter**: 简化异常检测（仅重建误差阈值）
- **PureRulesExpertAdapter**: 纯规则专家系统（不依赖机器学习模型）
- **UnifiedFusionEngine**: 统一融合引擎（标准化接口、权重融合）
- **BatchProcessingManager**: 批量处理管理器（文件夹级别处理）

### V1.0 原始架构（已废弃）
```
原始数据输入 → 特征对齐 → 数据分割 → 三算法并行处理 → 结果融合 → 统一输出
```

核心组件：
- UnifiedDetectionSystem: 主检测系统
- AlgorithmAdapters: 算法适配器（前驱信号、异常检测、专家系统）
- DataSegmentation: 数据分割模块（3分钟片段）
- UnifiedConfig: 配置管理

[2025-07-27 15:26:53] - Architecture update: 基于修正版分析完成统一检测系统重新实现方案设计
[2025-07-27 15:36:46] - New feature: 完成统一检测系统V2.0的完整代码实现指南
[2025-07-27 15:46:26] - New feature: 完成统一检测系统V2.0部署和测试指南