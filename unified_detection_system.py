#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一钻井算法检测系统
融合前驱信号检测、异常检测和专家经验三个算法

作者：AI Assistant
创建时间：2025-01-27
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
import subprocess
import joblib
from typing import Dict, List, Tuple, Optional, Union
import logging
from datetime import datetime
import warnings

from unified_config import CONFIG
from algorithm_adapters import AdapterFactory

warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class UnifiedDetectionSystem:
    """统一钻井检测系统"""

    def __init__(self,
                 weights: Dict[str, float] = None,
                 config: Dict = None):
        """
        初始化统一检测系统

        Args:
            weights: 算法权重配置 {'precursor': 0.5, 'anomaly': 0.3, 'expert': 0.2}
            config: 系统配置字典
        """
        self.config = config or CONFIG.get_default_config()
        self.weights = weights or self.config['weights']

        # 验证权重
        if not CONFIG.validate_weights(self.weights):
            raise ValueError("权重配置无效，权重之和必须等于1.0")

        # 创建算法适配器
        self.adapters = {
            'precursor': AdapterFactory.create_adapter('precursor', self.config['precursor']),
            'anomaly': AdapterFactory.create_adapter('anomaly', self.config['anomaly']),
            'expert': AdapterFactory.create_adapter('expert', self.config['expert'])
        }

        logger.info(f"初始化统一检测系统，权重配置: {self.weights}")
        logger.info(f"支持的算法: {list(self.adapters.keys())}")

    def align_features(self, data: pd.DataFrame, source: str = "unknown") -> pd.DataFrame:
        """
        特征对齐：将不同算法的输入统一到标准特征

        Args:
            data: 输入数据
            source: 数据来源标识

        Returns:
            对齐后的数据
        """
        logger.info(f"开始特征对齐，数据来源: {source}")
        logger.info(f"原始数据形状: {data.shape}, 列: {list(data.columns)}")

        # 创建标准格式的DataFrame
        aligned_data = pd.DataFrame()

        # 使用配置中的列名映射
        column_mapping = self.config['column_mapping']

        # 执行列名映射
        for original_col, standard_col in column_mapping.items():
            if original_col in data.columns:
                aligned_data[standard_col] = data[original_col]

        # 确保所有标准特征都存在
        standard_features = self.config['features']
        for feature in standard_features:
            if feature not in aligned_data.columns:
                if feature == 'date':
                    # 如果没有时间列，创建序号
                    aligned_data[feature] = range(len(data))
                else:
                    # 数值特征用0填充
                    aligned_data[feature] = 0.0
                    logger.warning(f"缺失特征 {feature}，用默认值填充")

        # 按标准顺序排列列
        aligned_data = aligned_data[standard_features]

        logger.info(f"特征对齐完成，输出形状: {aligned_data.shape}")
        return aligned_data

    def run_algorithm(self, algorithm_type: str, csv_file: str) -> Dict:
        """
        运行指定算法

        Args:
            algorithm_type: 算法类型 ('precursor', 'anomaly', 'expert')
            csv_file: CSV文件路径

        Returns:
            检测结果字典
        """
        if algorithm_type not in self.adapters:
            raise ValueError(f"不支持的算法类型: {algorithm_type}")

        logger.info(f"运行{algorithm_type}算法: {csv_file}")

        try:
            adapter = self.adapters[algorithm_type]
            result = adapter.run_algorithm(csv_file)

            # 确保结果包含必要字段
            if not result.get('success', False):
                logger.warning(f"{algorithm_type}算法执行失败: {result.get('error', 'Unknown error')}")
                return {"risk": 0.0, "label": 0, "error": result.get('error', 'Unknown error')}

            return {
                "risk": result.get('risk', 0.0),
                "label": result.get('label', 0),
                "details": result
            }

        except Exception as e:
            logger.error(f"{algorithm_type}算法异常: {str(e)}")
            return {"risk": 0.0, "label": 0, "error": str(e)}



    def fuse_results(self, precursor_result: Dict, anomaly_result: Dict, expert_result: Dict) -> Dict:
        """
        融合三个算法的检测结果

        Args:
            precursor_result: 前驱信号检测结果
            anomaly_result: 异常检测结果
            expert_result: 专家经验结果

        Returns:
            融合后的最终结果
        """
        logger.info("开始融合算法结果")

        # 提取各算法的风险分数
        precursor_risk = precursor_result.get('risk', 0.0)
        anomaly_risk = anomaly_result.get('risk', 0.0)
        expert_risk = expert_result.get('risk', 0.0)

        # 提取各算法的标签
        precursor_label = precursor_result.get('label', 0)
        anomaly_label = anomaly_result.get('label', 0)
        expert_label = expert_result.get('label', 0)

        # 加权融合风险分数
        fused_risk = (
            self.weights['precursor'] * precursor_risk +
            self.weights['anomaly'] * anomaly_risk +
            self.weights['expert'] * expert_risk
        )

        # 融合标签（加权投票）
        weighted_label_score = (
            self.weights['precursor'] * precursor_label +
            self.weights['anomaly'] * anomaly_label +
            self.weights['expert'] * expert_label
        )

        # 最终标签判断（阈值0.5）
        fused_label = 1 if weighted_label_score >= 0.5 else 0

        # 记录详细信息
        logger.info(f"算法结果 - 前驱: {precursor_risk:.3f}({precursor_label}), "
                   f"异常: {anomaly_risk:.3f}({anomaly_label}), "
                   f"专家: {expert_risk:.3f}({expert_label})")
        logger.info(f"融合结果 - 风险: {fused_risk:.3f}, 标签: {fused_label}")

        return {
            'risk': fused_risk,
            'label': fused_label,
            'details': {
                'precursor': precursor_result,
                'anomaly': anomaly_result,
                'expert': expert_result,
                'weighted_label_score': weighted_label_score
            }
        }

    def detect_single_file(self, csv_file: str) -> Dict:
        """
        对单个CSV文件进行统一检测

        Args:
            csv_file: CSV文件路径

        Returns:
            检测结果
        """
        logger.info(f"开始统一检测: {csv_file}")

        # 验证文件存在
        if not os.path.exists(csv_file):
            raise FileNotFoundError(f"文件不存在: {csv_file}")

        # 验证文件格式
        if not csv_file.lower().endswith('.csv'):
            raise ValueError(f"仅支持CSV格式文件: {csv_file}")

        # 运行三个算法
        precursor_result = self.run_algorithm('precursor', csv_file)
        anomaly_result = self.run_algorithm('anomaly', csv_file)
        expert_result = self.run_algorithm('expert', csv_file)

        # 融合结果
        fused_result = self.fuse_results(precursor_result, anomaly_result, expert_result)

        # 添加文件信息
        fused_result['filename'] = os.path.basename(csv_file)
        fused_result['filepath'] = csv_file

        return fused_result

    def batch_detect(self, input_dir: str, pattern: str = "*.csv", output_file: str = None,
                     segment_output: bool = True) -> str:
        """
        批量检测多个文件

        Args:
            input_dir: 输入目录
            pattern: 文件匹配模式
            output_file: 输出文件路径
            segment_output: 是否输出片段级别的结果（兼容前驱信号检测格式）

        Returns:
            输出文件路径
        """
        import glob

        logger.info(f"开始批量检测，目录: {input_dir}, 模式: {pattern}")

        # 查找匹配的文件
        search_pattern = os.path.join(input_dir, pattern)
        csv_files = glob.glob(search_pattern)

        if not csv_files:
            raise ValueError(f"未找到匹配的文件: {search_pattern}")

        logger.info(f"找到 {len(csv_files)} 个文件")

        # 批量处理
        all_results = []

        for i, csv_file in enumerate(csv_files, 1):
            logger.info(f"处理文件 {i}/{len(csv_files)}: {os.path.basename(csv_file)}")

            try:
                result = self.detect_single_file(csv_file)

                if segment_output and 'details' in result:
                    # 输出片段级别的结果（兼容前驱信号检测格式）
                    segment_results = self._extract_segment_results(result)
                    all_results.extend(segment_results)
                else:
                    # 输出文件级别的结果
                    all_results.append({
                        'Filename': result['filename'],
                        'Risk': result['risk'],
                        'Predicted_Label': result['label']
                    })

            except Exception as e:
                logger.error(f"处理文件失败 {csv_file}: {str(e)}")
                all_results.append({
                    'Filename': os.path.basename(csv_file),
                    'Risk': 0.0,
                    'Predicted_Label': 0
                })

        # 保存结果
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            prefix = "segment_results" if segment_output else "unified_results"
            output_file = f"{prefix}_{timestamp}.csv"

        results_df = pd.DataFrame(all_results)
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')

        logger.info(f"批量检测完成，结果保存到: {output_file}")
        logger.info(f"总记录数: {len(all_results)}, 预警记录数: {sum(r['Predicted_Label'] for r in all_results)}")

        return output_file

    def _extract_segment_results(self, detection_result: Dict) -> List[Dict]:
        """
        从检测结果中提取片段级别的结果

        Args:
            detection_result: 统一检测结果

        Returns:
            片段结果列表
        """
        segment_results = []

        # 获取各算法的片段结果
        details = detection_result.get('details', {})

        # 以前驱信号检测的片段为基准（因为它有文件名信息）
        precursor_segments = details.get('precursor', {}).get('segment_results', [])

        if precursor_segments:
            # 有片段结果，输出每个片段的融合结果
            for i, segment in enumerate(precursor_segments):
                filename = segment.get('filename', f"segment_{i}")

                # 获取对应的异常检测和专家系统结果
                anomaly_segments = details.get('anomaly', {}).get('segment_results', [])
                expert_segments = details.get('expert', {}).get('segment_results', [])

                anomaly_result = anomaly_segments[i] if i < len(anomaly_segments) else {'risk': 0.0, 'label': 0}
                expert_result = expert_segments[i] if i < len(expert_segments) else {'risk': 0.0, 'label': 0}

                # 计算片段级别的融合结果
                segment_risk = (
                    self.weights['precursor'] * segment.get('risk', 0.0) +
                    self.weights['anomaly'] * anomaly_result.get('risk', 0.0) +
                    self.weights['expert'] * expert_result.get('risk', 0.0)
                )

                segment_label_score = (
                    self.weights['precursor'] * segment.get('label', 0) +
                    self.weights['anomaly'] * anomaly_result.get('label', 0) +
                    self.weights['expert'] * expert_result.get('label', 0)
                )

                segment_label = 1 if segment_label_score >= 0.5 else 0

                segment_results.append({
                    'Filename': filename,
                    'Risk': segment_risk,
                    'Predicted_Label': segment_label
                })
        else:
            # 没有片段结果，返回文件级别结果
            segment_results.append({
                'Filename': detection_result['filename'],
                'Risk': detection_result['risk'],
                'Predicted_Label': detection_result['label']
            })

        return segment_results


def demo_detection():
    """演示检测功能"""
    print("🚀 统一钻井检测系统演示")
    print("=" * 50)

    # 创建检测系统
    detector = UnifiedDetectionSystem()

    # 查找当前目录下的CSV文件
    import glob
    csv_files = glob.glob("*.csv")

    if not csv_files:
        print("❌ 当前目录下没有找到CSV文件")
        print("请将要检测的CSV文件放在当前目录下，然后重新运行")
        return

    print(f"📁 找到 {len(csv_files)} 个CSV文件:")
    for i, file in enumerate(csv_files, 1):
        print(f"  {i}. {file}")

    # 选择处理模式
    print("\n🔧 处理模式:")
    print("1. 单文件检测（选择一个文件）")
    print("2. 批量检测（处理所有文件）")

    try:
        choice = input("\n请选择模式 (1/2): ").strip()

        if choice == "1":
            # 单文件模式
            if len(csv_files) == 1:
                selected_file = csv_files[0]
                print(f"自动选择文件: {selected_file}")
            else:
                print("\n请选择要检测的文件:")
                for i, file in enumerate(csv_files, 1):
                    print(f"  {i}. {file}")

                file_choice = input("请输入文件编号: ").strip()
                try:
                    file_index = int(file_choice) - 1
                    if 0 <= file_index < len(csv_files):
                        selected_file = csv_files[file_index]
                    else:
                        print("❌ 无效的文件编号")
                        return
                except ValueError:
                    print("❌ 请输入有效的数字")
                    return

            print(f"\n🔍 开始检测文件: {selected_file}")
            result = detector.detect_single_file(selected_file)

            print(f"\n📊 检测结果:")
            print(f"文件: {result['filename']}")
            print(f"风险分数: {result['risk']:.3f}")
            print(f"预测标签: {result['label']} ({'预警' if result['label'] == 1 else '正常'})")

        elif choice == "2":
            # 批量模式
            print(f"\n🔍 开始批量检测 {len(csv_files)} 个文件...")

            # 创建临时目录并复制文件
            temp_dir = "temp_batch_input"
            os.makedirs(temp_dir, exist_ok=True)

            for file in csv_files:
                import shutil
                shutil.copy2(file, temp_dir)

            try:
                output_file = detector.batch_detect(
                    input_dir=temp_dir,
                    pattern="*.csv",
                    segment_output=True  # 默认输出片段级别结果
                )

                print(f"✅ 批量检测完成！结果保存到: {output_file}")

                # 显示结果摘要
                results_df = pd.read_csv(output_file)
                total_segments = len(results_df)
                warning_segments = sum(results_df['Predicted_Label'])

                print(f"\n📈 结果摘要:")
                print(f"总片段数: {total_segments}")
                print(f"预警片段数: {warning_segments}")
                print(f"预警比例: {warning_segments/total_segments*100:.1f}%")

            finally:
                # 清理临时目录
                import shutil
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)

        else:
            print("❌ 无效的选择")

    except KeyboardInterrupt:
        print("\n\n⏹️ 用户取消操作")
    except Exception as e:
        print(f"\n❌ 检测过程中出现错误: {str(e)}")


def main():
    """主函数"""
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        # 有参数，使用命令行模式
        parser = argparse.ArgumentParser(description='统一钻井检测系统')
        parser.add_argument('input', help='输入文件或目录路径')
        parser.add_argument('-o', '--output', help='输出文件路径')
        parser.add_argument('--batch', action='store_true', help='批量处理模式')
        parser.add_argument('--pattern', default='*.csv', help='批量处理时的文件匹配模式')
        parser.add_argument('--segment-output', action='store_true', default=True,
                           help='输出片段级别结果（兼容前驱信号检测格式）')
        parser.add_argument('--file-output', action='store_true',
                           help='输出文件级别结果（覆盖--segment-output）')
        parser.add_argument('--precursor-weight', type=float, default=0.5, help='前驱信号检测权重')
        parser.add_argument('--anomaly-weight', type=float, default=0.3, help='异常检测权重')
        parser.add_argument('--expert-weight', type=float, default=0.2, help='专家经验权重')
        parser.add_argument('-v', '--verbose', action='store_true', help='详细日志')

        args = parser.parse_args()

        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

        try:
            # 配置权重
            weights = {
                'precursor': args.precursor_weight,
                'anomaly': args.anomaly_weight,
                'expert': args.expert_weight
            }

            # 创建检测系统
            detector = UnifiedDetectionSystem(weights=weights)

            if args.batch:
                # 批量处理模式
                if not os.path.isdir(args.input):
                    raise ValueError(f"批量模式需要输入目录: {args.input}")

                # 确定输出模式
                segment_output = args.segment_output and not args.file_output

                output_file = detector.batch_detect(
                    input_dir=args.input,
                    pattern=args.pattern,
                    output_file=args.output,
                    segment_output=segment_output
                )

                output_mode = "片段级别" if segment_output else "文件级别"
                print(f"✅ 批量检测完成！{output_mode}结果保存到: {output_file}")

            else:
                # 单文件处理模式
                if not os.path.isfile(args.input):
                    raise ValueError(f"单文件模式需要输入文件: {args.input}")

                result = detector.detect_single_file(args.input)

                print(f"\n📊 检测结果:")
                print(f"文件: {result['filename']}")
                print(f"风险分数: {result['risk']:.3f}")
                print(f"预测标签: {result['label']} ({'预警' if result['label'] == 1 else '正常'})")

                # 保存单文件结果
                if args.output:
                    results_df = pd.DataFrame([{
                        'Filename': result['filename'],
                        'Risk': result['risk'],
                        'Predicted_Label': result['label']
                    }])
                    results_df.to_csv(args.output, index=False, encoding='utf-8-sig')
                    print(f"结果已保存到: {args.output}")

        except Exception as e:
            logger.error(f"程序执行失败: {str(e)}")
            print(f"❌ 错误: {str(e)}")
            return 1

    else:
        # 没有参数，使用演示模式
        demo_detection()

    return 0


if __name__ == "__main__":
    exit(main())
