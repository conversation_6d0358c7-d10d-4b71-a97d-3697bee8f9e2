#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一检测系统快速验证脚本
用于验证系统各组件是否正常工作

作者：AI Assistant
创建时间：2025-01-27
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile
import shutil

def create_test_data():
    """创建测试数据"""
    print("📝 创建测试数据...")
    
    # 创建3分钟的测试数据（180秒，每秒一个数据点）
    timestamps = pd.date_range(
        start='2025-01-27 10:00:00',
        periods=180,
        freq='1S'
    )
    
    # 创建符合前驱信号检测格式的数据
    data = {
        'DEP': np.random.uniform(1000, 1200, 180),
        'BITDEP': np.random.uniform(1000, 1200, 180),
        'HOKHEI': np.random.uniform(50, 100, 180),
        'DRITIME': np.random.uniform(0, 60, 180),
        'WOB': np.random.uniform(10, 50, 180),
        'HKLD': np.random.uniform(100, 200, 180),
        'RPM': np.random.uniform(80, 120, 180),
        'TOR': np.random.uniform(5, 15, 180),
        'SPP': np.random.uniform(10, 30, 180),
        'date': timestamps
    }
    
    df = pd.DataFrame(data)
    
    # 保存测试文件
    test_file = "test_well_data.csv"
    df.to_csv(test_file, index=False)
    
    print(f"✅ 测试数据已创建: {test_file}")
    print(f"   数据形状: {df.shape}")
    print(f"   时间范围: {df['date'].min()} 到 {df['date'].max()}")
    
    return test_file

def test_data_segmentation():
    """测试数据分割功能"""
    print("\n🔧 测试数据分割功能...")
    
    try:
        from data_segmentation import DataSegmentation
        
        # 创建测试数据
        test_file = create_test_data()
        
        # 创建分割器
        segmentation = DataSegmentation(segment_duration_minutes=3)
        
        # 执行分割
        output_dir, segment_files = segmentation.segment_data(test_file)
        
        print(f"✅ 数据分割成功")
        print(f"   输出目录: {output_dir}")
        print(f"   分割文件数: {len(segment_files)}")
        
        # 检查分割文件
        for i, segment_file in enumerate(segment_files[:3]):  # 只显示前3个
            segment_df = pd.read_csv(segment_file)
            print(f"   片段{i+1}: {os.path.basename(segment_file)} ({len(segment_df)}行)")
        
        # 清理
        segmentation.cleanup_temp_dirs()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据分割测试失败: {str(e)}")
        return False

def test_algorithm_adapters():
    """测试算法适配器"""
    print("\n🔧 测试算法适配器...")
    
    try:
        from algorithm_adapters import AdapterFactory
        from unified_config import CONFIG
        
        # 测试创建适配器
        adapters = {}
        for algo_type in ['precursor', 'anomaly', 'expert']:
            try:
                adapter = AdapterFactory.create_adapter(algo_type, CONFIG.get_algorithm_config(algo_type))
                adapters[algo_type] = adapter
                print(f"✅ {algo_type}适配器创建成功")
            except Exception as e:
                print(f"❌ {algo_type}适配器创建失败: {str(e)}")
        
        return len(adapters) > 0
        
    except Exception as e:
        print(f"❌ 算法适配器测试失败: {str(e)}")
        return False

def test_unified_system():
    """测试统一检测系统"""
    print("\n🔧 测试统一检测系统...")
    
    try:
        from unified_detection_system import UnifiedDetectionSystem
        
        # 创建检测系统
        detector = UnifiedDetectionSystem()
        print("✅ 统一检测系统创建成功")
        
        # 创建测试数据
        test_file = create_test_data()
        
        # 测试特征对齐
        test_df = pd.read_csv(test_file)
        aligned_df = detector.align_features(test_df, "test")
        print(f"✅ 特征对齐成功: {test_df.shape} -> {aligned_df.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一检测系统测试失败: {str(e)}")
        return False

def test_config():
    """测试配置模块"""
    print("\n🔧 测试配置模块...")
    
    try:
        from unified_config import CONFIG
        
        # 测试配置获取
        config = CONFIG.get_default_config()
        print(f"✅ 默认配置获取成功")
        
        # 测试权重验证
        valid_weights = {'precursor': 0.5, 'anomaly': 0.3, 'expert': 0.2}
        invalid_weights = {'precursor': 0.6, 'anomaly': 0.3, 'expert': 0.2}
        
        assert CONFIG.validate_weights(valid_weights) == True
        assert CONFIG.validate_weights(invalid_weights) == False
        print("✅ 权重验证功能正常")
        
        # 测试特征配置
        features = CONFIG.STANDARD_FEATURES
        print(f"✅ 标准特征配置: {len(features)}个特征")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置模块测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 统一检测系统快速验证")
    print("=" * 50)
    
    # 记录测试结果
    test_results = {}
    
    # 执行各项测试
    test_results['配置模块'] = test_config()
    test_results['数据分割'] = test_data_segmentation()
    test_results['算法适配器'] = test_algorithm_adapters()
    test_results['统一系统'] = test_unified_system()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统基本功能正常")
        print("\n💡 建议下一步:")
        print("  1. 运行 python unified_detection_system.py 进行演示")
        print("  2. 准备真实数据进行完整测试")
    else:
        print("⚠️  部分测试失败，请检查相关模块")
    
    # 清理测试文件
    test_files = ["test_well_data.csv"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 清理测试文件: {file}")

if __name__ == "__main__":
    main()
