# 统一检测系统V2.0代码实现指南

## 📋 概述

基于《基于修正版分析的统一检测系统重新实现方案.md》，本文档提供V2.0系统的具体代码实现。重点关注功能的正确性和可运行性，确保三算法能够稳定融合运行。

## 🏗️ 项目结构

```
unified_detection_system_v2/
├── core/
│   ├── __init__.py
│   ├── data_processor.py          # 数据预处理器
│   ├── algorithm_adapters.py      # 算法适配器
│   ├── fusion_engine.py           # 融合引擎
│   └── batch_processor.py         # 批量处理器
├── config/
│   ├── __init__.py
│   └── config.py                  # 配置管理
├── utils/
│   ├── __init__.py
│   ├── data_converter.py          # 数据转换工具
│   ├── folder_manager.py          # 文件夹管理
│   └── logger.py                  # 日志工具
├── main.py                        # 主入口
├── config.yaml                    # 配置文件
└── requirements.txt               # 依赖包
```

## 🔧 核心模块实现

### 1. 配置管理 (config/config.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import yaml
import os
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            return self.get_default_config()
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'system': {
                'version': '2.0',
                'name': '统一钻井检测系统V2.0'
            },
            'weights': {
                'precursor': 0.5,
                'anomaly': 0.3,
                'expert': 0.2
            },
            'algorithms': {
                'precursor': {
                    'model_path': './前驱信号检测/checkpoints',
                    'data_path': './前驱信号检测/dataset2',
                    'sequence_length': 152,
                    'features': 10
                },
                'anomaly': {
                    'model_path': './异常检测/checkpoints',
                    'train_data_path': './异常检测/dataset/train自201H54-3.npy',
                    'anomaly_ratio': 1.0,
                    'features': 12
                },
                'expert': {
                    'window_size': 30,
                    'score_threshold': 4,
                    'rules_config': {
                        'depth_change_threshold': 0.001,
                        'bit_change_threshold': 0.001,
                        'rpm_threshold': 5,
                        'hook_height_change_threshold': 0.01,
                        'hookload_change_threshold': 3,
                        'flow_diff_threshold': 15
                    }
                }
            },
            'data_processing': {
                'csv_encoding': 'utf-8',
                'npy_precision': 'float32',
                'missing_value_strategy': 'default'
            },
            'output': {
                'save_detailed_results': True,
                'save_summary_report': True,
                'result_format': 'json'
            }
        }
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value

# 全局配置实例
config_manager = ConfigManager()
```

### 2. 数据预处理器 (core/data_processor.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理器V2.0
"""

import os
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Tuple, Optional
import tempfile
import shutil

from config.config import config_manager

class DataProcessorV2:
    """数据预处理器V2.0"""
    
    def __init__(self):
        self.config = config_manager.config
        self.csv_to_npy_converter = CSVToNPYConverter()
        self.folder_manager = FolderStructureManager()
    
    def prepare_data_for_algorithms(self, csv_file: str) -> Dict[str, str]:
        """为三个算法准备数据"""
        results = {}
        
        try:
            # 1. 为异常检测准备NPY数据
            npy_file = self.csv_to_npy_converter.convert_csv_to_npy(csv_file)
            results['anomaly'] = npy_file
            
            # 2. 为前驱信号检测准备文件夹结构
            folder_structure = self.folder_manager.create_earlysignal_structure([csv_file])
            results['precursor'] = folder_structure['normal_path']
            
            # 3. 为专家规则准备标准CSV
            processed_csv = self.prepare_csv_for_expert_rules(csv_file)
            results['expert'] = processed_csv
            
            return results
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return {}
    
    def prepare_csv_for_expert_rules(self, csv_file: str) -> str:
        """为专家规则准备标准CSV格式"""
        try:
            df = pd.read_csv(csv_file, encoding=self.config['data_processing']['csv_encoding'])
            
            # 列重命名映射
            column_mapping = {
                'DEP': 'Well_Depth',
                'BITDEP': 'Bit_Depth',
                'HOKHEI': 'Hook_Height',
                'HKLD': 'Hookload',
                'RPM': 'RPM',
                'TOR': 'Torque',
                'FLOWIN': 'Flow_In',
                'FLOWOUT': 'Flow_Out',
                'SPP': 'Standpipe_Pressure'
            }
            
            # 重命名列
            df_renamed = df.rename(columns=column_mapping)
            
            # 处理缺失的流量列
            if 'Flow_In' not in df_renamed.columns:
                df_renamed['Flow_In'] = 30.0
            if 'Flow_Out' not in df_renamed.columns:
                df_renamed['Flow_Out'] = 25.0
            
            # 保存处理后的文件
            output_file = csv_file.replace('.csv', '_expert_processed.csv')
            df_renamed.to_csv(output_file, index=False, encoding='utf-8')
            
            return output_file
            
        except Exception as e:
            print(f"专家规则数据预处理失败: {e}")
            return csv_file

class CSVToNPYConverter:
    """CSV到NPY转换器"""
    
    def __init__(self):
        self.config = config_manager.config
        self.scaler = StandardScaler()
        self._load_train_scaler()
    
    def _load_train_scaler(self):
        """加载训练数据的标准化器"""
        try:
            train_data_path = self.config['algorithms']['anomaly']['train_data_path']
            if os.path.exists(train_data_path):
                train_data = np.load(train_data_path, allow_pickle=True)
                train_features = train_data[:, :-1]  # 去除时间戳
                self.scaler.fit(train_features)
            else:
                print(f"警告：训练数据文件不存在: {train_data_path}")
        except Exception as e:
            print(f"加载训练数据标准化器失败: {e}")
    
    def convert_csv_to_npy(self, csv_file: str) -> str:
        """转换CSV文件为NPY格式"""
        try:
            # 1. 读取CSV数据
            df = pd.read_csv(csv_file, encoding='utf-8')
            
            # 2. 提取12维特征
            features = self.extract_12_features(df)
            
            # 3. 添加时间戳列
            timestamps = np.arange(len(features)).reshape(-1, 1)
            npy_data = np.hstack([features, timestamps])
            
            # 4. 保存NPY文件
            output_file = csv_file.replace('.csv', '.npy')
            np.save(output_file, npy_data.astype(np.float32))
            
            return output_file
            
        except Exception as e:
            print(f"CSV到NPY转换失败: {e}")
            return ""
    
    def extract_12_features(self, df: pd.DataFrame) -> np.ndarray:
        """从CSV提取12维特征"""
        # 标准10维特征
        standard_features = [
            'DEP', 'BITDEP', 'HOKHEI', 'HKLD', 'RPM',
            'TOR', 'FLOWIN', 'FLOWOUT', 'SPP', 'WOB'
        ]
        
        # 额外2维特征（异常检测特有）
        extra_features = ['PUMP_PRESSURE', 'MUD_DENSITY']
        
        # 提取特征矩阵
        feature_matrix = []
        for col in standard_features + extra_features:
            if col in df.columns:
                feature_matrix.append(df[col].values)
            else:
                # 使用默认值
                default_values = self.get_default_feature_values(col, len(df))
                feature_matrix.append(default_values)
        
        return np.column_stack(feature_matrix)
    
    def get_default_feature_values(self, feature_name: str, length: int) -> np.ndarray:
        """获取特征的默认值"""
        defaults = {
            'DEP': 2000.0,
            'BITDEP': 2000.0,
            'HOKHEI': 10.0,
            'HKLD': 150.0,
            'RPM': 80.0,
            'TOR': 15.0,
            'FLOWIN': 30.0,
            'FLOWOUT': 25.0,
            'SPP': 12.0,
            'WOB': 120.0,
            'PUMP_PRESSURE': 12.0,
            'MUD_DENSITY': 1.2
        }
        
        default_value = defaults.get(feature_name, 0.0)
        return np.full(length, default_value)

class FolderStructureManager:
    """文件夹结构管理器"""
    
    def create_earlysignal_structure(self, csv_files: List[str]) -> Dict[str, str]:
        """创建前驱信号检测需要的文件夹结构"""
        try:
            # 1. 创建临时目录结构
            temp_dir = tempfile.mkdtemp(prefix='earlysignal_')
            normal_path = os.path.join(temp_dir, 'normal')
            earlysignal_path = os.path.join(temp_dir, 'earlysignal2')
            
            os.makedirs(normal_path, exist_ok=True)
            os.makedirs(earlysignal_path, exist_ok=True)
            
            # 2. 处理CSV文件
            for csv_file in csv_files:
                df = pd.read_csv(csv_file, encoding='utf-8')
                processed_df = self.preprocess_for_earlysignal(df)
                
                # 保存到normal文件夹
                output_file = os.path.join(normal_path, os.path.basename(csv_file))
                processed_df.to_csv(output_file, index=False, encoding='utf-8')
            
            return {
                'normal_path': normal_path,
                'earlysignal_path': earlysignal_path,
                'temp_dir': temp_dir,
                'processed_files': len(csv_files)
            }
            
        except Exception as e:
            print(f"创建前驱信号文件夹结构失败: {e}")
            return {}
    
    def preprocess_for_earlysignal(self, df: pd.DataFrame) -> pd.DataFrame:
        """为前驱信号检测预处理数据"""
        # 提取10维特征
        required_columns = [
            'DEP', 'BITDEP', 'HOKHEI', 'HKLD', 'RPM',
            'TOR', 'FLOWIN', 'FLOWOUT', 'SPP', 'WOB'
        ]
        
        # 确保所有列都存在
        for col in required_columns:
            if col not in df.columns:
                df[col] = 0.0  # 默认值
        
        # 选择需要的列
        processed_df = df[required_columns].copy()
        
        # 数据清洗
        processed_df = processed_df.fillna(method='forward').fillna(0)
        
        return processed_df

### 3. 算法适配器 (core/algorithm_adapters.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法适配器V2.0 - 重新设计版本
"""

import os
import sys
import pandas as pd
import numpy as np
import subprocess
import re
import tempfile
import shutil
from typing import Dict, List, Optional
from abc import ABC, abstractmethod

from config.config import config_manager

class BaseAlgorithmAdapter(ABC):
    """算法适配器基类"""

    def __init__(self):
        self.config = config_manager.config

    @abstractmethod
    def predict_single_file(self, input_file: str) -> Dict:
        """单文件预测接口"""
        pass

class SimplifiedAnomalyDetectionAdapter(BaseAlgorithmAdapter):
    """简化异常检测适配器 - 仅重建误差阈值"""

    def __init__(self):
        super().__init__()
        self.anomaly_config = self.config['algorithms']['anomaly']
        self.model_path = self.anomaly_config['model_path']
        self.train_data_path = self.anomaly_config['train_data_path']
        self.anomaly_ratio = self.anomaly_config['anomaly_ratio']

    def predict_single_file(self, npy_file: str) -> Dict:
        """单文件异常检测预测"""
        try:
            # 检查输入文件
            if not os.path.exists(npy_file):
                return {
                    'success': False,
                    'error': f'NPY文件不存在: {npy_file}',
                    'risk': 0.0,
                    'label': 0
                }

            # 调用异常检测算法
            result = self.run_anomaly_detection(npy_file)

            if result['success']:
                return {
                    'success': True,
                    'risk': result['risk'],
                    'label': result['label'],
                    'anomaly_ratio': result['anomaly_ratio'],
                    'method': 'reconstruction_threshold'
                }
            else:
                return result

        except Exception as e:
            return {
                'success': False,
                'error': f'异常检测执行失败: {str(e)}',
                'risk': 0.0,
                'label': 0
            }

    def run_anomaly_detection(self, npy_file: str) -> Dict:
        """运行异常检测算法"""
        try:
            # 构建命令
            anomaly_dir = os.path.dirname(self.model_path)
            run_script = os.path.join(anomaly_dir, 'run.py')

            if not os.path.exists(run_script):
                return {
                    'success': False,
                    'error': f'异常检测运行脚本不存在: {run_script}'
                }

            # 执行异常检测
            cmd = [
                sys.executable, run_script,
                '--task_name', 'anomaly_detection',
                '--is_anomaly', '1',
                '--model', 'FEDformer',
                '--test_file', os.path.basename(npy_file),
                '--no_label', '1'
            ]

            # 切换到异常检测目录
            original_cwd = os.getcwd()
            os.chdir(anomaly_dir)

            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )

                if result.returncode == 0:
                    # 解析输出
                    parsed_result = self.parse_anomaly_output(result.stdout)
                    return parsed_result
                else:
                    return {
                        'success': False,
                        'error': f'异常检测执行失败: {result.stderr}'
                    }

            finally:
                os.chdir(original_cwd)

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': '异常检测执行超时'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'异常检测执行异常: {str(e)}'
            }

    def parse_anomaly_output(self, stdout_text: str) -> Dict:
        """解析异常检测输出"""
        try:
            # 提取异常检测比例
            ratio_match = re.search(r'异常检测比例:\s*(\d+\.\d+)%', stdout_text)
            if ratio_match:
                anomaly_ratio = float(ratio_match.group(1))
                risk = anomaly_ratio / 100.0
                label = 1 if anomaly_ratio > 10.0 else 0

                return {
                    'success': True,
                    'risk': risk,
                    'label': label,
                    'anomaly_ratio': anomaly_ratio
                }
            else:
                return {
                    'success': False,
                    'error': '无法解析异常检测结果'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'输出解析失败: {str(e)}'
            }

class PureRulesExpertAdapter(BaseAlgorithmAdapter):
    """纯规则专家系统适配器 - 不依赖机器学习模型"""

    def __init__(self):
        super().__init__()
        self.expert_config = self.config['algorithms']['expert']
        self.window_size = self.expert_config['window_size']
        self.score_threshold = self.expert_config['score_threshold']
        self.rules_config = self.expert_config['rules_config']

    def predict_single_file(self, csv_file: str) -> Dict:
        """单文件专家规则预测"""
        try:
            # 读取和预处理数据
            df = pd.read_csv(csv_file, encoding='utf-8')

            # 检查数据长度
            if len(df) < self.window_size:
                return {
                    'success': False,
                    'error': f'数据长度不足，需要至少{self.window_size}个数据点',
                    'risk': 0.0,
                    'label': 0
                }

            # 滑动窗口规则计算
            results = []
            for i in range(self.window_size, len(df)):
                features = self.calculate_features(df, i)
                score = self.calculate_rule_score(features)

                results.append({
                    'timestamp': i,
                    'rule_score': score,
                    'risk': score / 6.0,
                    'label': 1 if score >= self.score_threshold else 0
                })

            # 汇总结果
            if not results:
                return {
                    'success': False,
                    'error': '无有效结果',
                    'risk': 0.0,
                    'label': 0
                }

            scores = [r['rule_score'] for r in results]
            avg_risk = np.mean([r['risk'] for r in results])
            max_score = max(scores)
            final_label = 1 if max_score >= self.score_threshold else 0

            return {
                'success': True,
                'risk': avg_risk,
                'label': final_label,
                'avg_score': np.mean(scores),
                'max_score': max_score,
                'alert_points': sum(r['label'] for r in results),
                'total_points': len(results)
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'专家规则执行失败: {str(e)}',
                'risk': 0.0,
                'label': 0
            }

    def calculate_features(self, df: pd.DataFrame, i: int) -> Dict:
        """计算单个时间点的特征"""
        w = df.iloc[i - self.window_size:i]  # 窗口数据
        row = df.iloc[i]                     # 当前行

        # 6个核心特征计算
        features = {
            'depth_change': w["Well_Depth"].max() - w["Well_Depth"].min(),
            'bit_change': w["Bit_Depth"].max() - w["Bit_Depth"].min(),
            'rpm_now': row["RPM"],
            'flow_diff': (w["Flow_In"] - w["Flow_Out"]).mean(),
            'hook_height_change': w["Hook_Height"].max() - w["Hook_Height"].min(),
            'hookload_change': w["Hookload"].max() - w["Hookload"].min()
        }

        # 复合特征
        features['flow_rpm_pattern'] = 1 if (
            features['rpm_now'] < self.rules_config['rpm_threshold'] and
            features['flow_diff'] > self.rules_config['flow_diff_threshold']
        ) else 0

        return features

    def calculate_rule_score(self, features: Dict) -> int:
        """计算规则得分（0-6分）"""
        score = 0

        # 规则1：井深变化小
        if features['depth_change'] < self.rules_config['depth_change_threshold']:
            score += 1

        # 规则2：钻头深度变化小
        if features['bit_change'] < self.rules_config['bit_change_threshold']:
            score += 1

        # 规则3：转速低
        if features['rpm_now'] < self.rules_config['rpm_threshold']:
            score += 1

        # 规则4：大钩高度变化小
        if features['hook_height_change'] < self.rules_config['hook_height_change_threshold']:
            score += 1

        # 规则5：大钩负荷变化大
        if features['hookload_change'] > self.rules_config['hookload_change_threshold']:
            score += 1

        # 规则6：流量-转速模式
        if features['flow_rpm_pattern'] == 1:
            score += 1

        return score

class PrecursorDetectionAdapter(BaseAlgorithmAdapter):
    """前驱信号检测适配器"""

    def __init__(self):
        super().__init__()
        self.precursor_config = self.config['algorithms']['precursor']
        self.model_path = self.precursor_config['model_path']
        self.data_path = self.precursor_config['data_path']

    def predict_single_file(self, folder_path: str) -> Dict:
        """单文件前驱信号检测预测"""
        try:
            # 检查文件夹结构
            normal_path = os.path.join(folder_path, 'normal')
            if not os.path.exists(normal_path):
                return {
                    'success': False,
                    'error': f'前驱信号检测文件夹结构不正确: {folder_path}',
                    'risk': 0.0,
                    'label': 0
                }

            # 调用前驱信号检测算法
            result = self.run_precursor_detection(folder_path)

            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'前驱信号检测执行失败: {str(e)}',
                'risk': 0.0,
                'label': 0
            }

    def run_precursor_detection(self, folder_path: str) -> Dict:
        """运行前驱信号检测算法"""
        try:
            # 构建命令
            precursor_dir = os.path.dirname(self.model_path)
            run_script = os.path.join(precursor_dir, 'run.py')

            if not os.path.exists(run_script):
                return {
                    'success': False,
                    'error': f'前驱信号检测运行脚本不存在: {run_script}'
                }

            # 执行前驱信号检测
            cmd = [
                sys.executable, run_script,
                '--task_name', 'anomaly_detection',
                '--is_anomaly', '1',
                '--model', 'PatchTST',
                '--data', 'custom',
                '--root_path', folder_path,
                '--data_path', 'normal',
                '--flag', 'Earlysignaldet'
            ]

            # 切换到前驱信号检测目录
            original_cwd = os.getcwd()
            os.chdir(precursor_dir)

            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )

                if result.returncode == 0:
                    # 解析输出
                    parsed_result = self.parse_precursor_output(result.stdout)
                    return parsed_result
                else:
                    return {
                        'success': False,
                        'error': f'前驱信号检测执行失败: {result.stderr}'
                    }

            finally:
                os.chdir(original_cwd)

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': '前驱信号检测执行超时'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'前驱信号检测执行异常: {str(e)}'
            }

    def parse_precursor_output(self, stdout_text: str) -> Dict:
        """解析前驱信号检测输出"""
        try:
            # 查找预测结果文件
            predictions_file = None
            for line in stdout_text.split('\n'):
                if 'predictions' in line and '.csv' in line:
                    predictions_file = line.strip()
                    break

            if predictions_file and os.path.exists(predictions_file):
                # 读取预测结果
                df = pd.read_csv(predictions_file)

                if 'risk_score' in df.columns and 'prediction' in df.columns:
                    avg_risk = df['risk_score'].mean()
                    final_label = 1 if df['prediction'].sum() > 0 else 0

                    return {
                        'success': True,
                        'risk': avg_risk,
                        'label': final_label,
                        'predictions_file': predictions_file
                    }

            # 如果没有找到预测文件，尝试从输出中解析
            risk_match = re.search(r'风险分数:\s*(\d+\.\d+)', stdout_text)
            label_match = re.search(r'预测标签:\s*(\d+)', stdout_text)

            if risk_match and label_match:
                risk = float(risk_match.group(1))
                label = int(label_match.group(1))

                return {
                    'success': True,
                    'risk': risk,
                    'label': label
                }
            else:
                return {
                    'success': False,
                    'error': '无法解析前驱信号检测结果'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'前驱信号检测输出解析失败: {str(e)}'
            }
```

### 4. 融合引擎 (core/fusion_engine.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一融合引擎V2.0
"""

import os
import numpy as np
from typing import Dict, List, Optional
import tempfile
import shutil

from config.config import config_manager
from core.data_processor import DataProcessorV2
from core.algorithm_adapters import (
    SimplifiedAnomalyDetectionAdapter,
    PureRulesExpertAdapter,
    PrecursorDetectionAdapter
)

class UnifiedFusionEngine:
    """统一融合引擎V2.0"""

    def __init__(self, weights: Optional[Dict[str, float]] = None):
        self.config = config_manager.config
        self.weights = weights or self.config['weights']

        # 初始化组件
        self.data_processor = DataProcessorV2()
        self.adapters = {
            'precursor': PrecursorDetectionAdapter(),
            'anomaly': SimplifiedAnomalyDetectionAdapter(),
            'expert': PureRulesExpertAdapter()
        }

    def predict_single_file(self, csv_file: str) -> Dict:
        """统一的单文件预测接口"""
        try:
            # 1. 数据预处理
            processed_data = self.data_processor.prepare_data_for_algorithms(csv_file)

            if not processed_data:
                return {
                    'success': False,
                    'error': '数据预处理失败',
                    'risk': 0.0,
                    'label': 0
                }

            # 2. 执行三个算法
            algorithm_results = {}

            for algo_name, adapter in self.adapters.items():
                try:
                    if algo_name in processed_data:
                        algo_input = processed_data[algo_name]
                        result = adapter.predict_single_file(algo_input)
                        algorithm_results[algo_name] = result
                    else:
                        algorithm_results[algo_name] = {
                            'success': False,
                            'error': f'{algo_name}数据预处理失败',
                            'risk': 0.0,
                            'label': 0
                        }
                except Exception as e:
                    algorithm_results[algo_name] = {
                        'success': False,
                        'error': f'{algo_name}执行异常: {str(e)}',
                        'risk': 0.0,
                        'label': 0
                    }

            # 3. 结果融合
            fused_result = self.fuse_results(algorithm_results)

            # 4. 清理临时文件
            self.cleanup_temp_files(processed_data)

            return fused_result

        except Exception as e:
            return {
                'success': False,
                'error': f'融合引擎执行失败: {str(e)}',
                'risk': 0.0,
                'label': 0
            }

    def fuse_results(self, algorithm_results: Dict) -> Dict:
        """权重融合算法结果"""
        try:
            # 1. 提取有效结果
            valid_results = {
                k: v for k, v in algorithm_results.items()
                if v.get('success', False)
            }

            if not valid_results:
                return {
                    'success': False,
                    'error': '所有算法都执行失败',
                    'risk': 0.0,
                    'label': 0,
                    'algorithm_results': algorithm_results
                }

            # 2. 权重融合风险分数
            total_weight = sum(self.weights[k] for k in valid_results.keys())

            if total_weight == 0:
                weighted_risk = 0.0
                weighted_votes = 0.0
            else:
                weighted_risk = sum(
                    self.weights[k] * v['risk'] for k, v in valid_results.items()
                ) / total_weight

                # 3. 投票融合标签
                weighted_votes = sum(
                    self.weights[k] * v['label'] for k, v in valid_results.items()
                ) / total_weight

            final_label = 1 if weighted_votes > 0.5 else 0

            return {
                'success': True,
                'risk': weighted_risk,
                'label': final_label,
                'confidence': len(valid_results) / len(self.adapters),
                'algorithm_results': algorithm_results,
                'fusion_weights': self.weights,
                'valid_algorithms': list(valid_results.keys())
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'结果融合失败: {str(e)}',
                'risk': 0.0,
                'label': 0,
                'algorithm_results': algorithm_results
            }

    def cleanup_temp_files(self, processed_data: Dict):
        """清理临时文件"""
        try:
            for algo_name, file_path in processed_data.items():
                if algo_name == 'precursor':
                    # 清理前驱信号检测的临时文件夹
                    temp_dir = os.path.dirname(file_path)
                    if temp_dir and os.path.exists(temp_dir) and 'earlysignal_' in temp_dir:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                elif algo_name == 'anomaly':
                    # 清理NPY文件
                    if file_path and os.path.exists(file_path) and file_path.endswith('.npy'):
                        os.remove(file_path)
                elif algo_name == 'expert':
                    # 清理专家规则处理后的CSV文件
                    if file_path and os.path.exists(file_path) and '_expert_processed.csv' in file_path:
                        os.remove(file_path)
        except Exception as e:
            print(f"清理临时文件失败: {e}")

### 5. 批量处理器 (core/batch_processor.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理器V2.0
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import concurrent.futures
from pathlib import Path

from config.config import config_manager
from core.fusion_engine import UnifiedFusionEngine

class BatchProcessingManager:
    """批量处理管理器V2.0"""

    def __init__(self, fusion_engine: Optional[UnifiedFusionEngine] = None):
        self.config = config_manager.config
        self.fusion_engine = fusion_engine or UnifiedFusionEngine()
        self.file_scanner = FileScanner()
        self.result_aggregator = ResultAggregator()

    def process_folder(self, input_folder: str, output_folder: Optional[str] = None) -> Dict:
        """处理整个文件夹"""
        try:
            # 1. 扫描输入文件
            csv_files = self.file_scanner.scan_csv_files(input_folder)

            if not csv_files:
                return {
                    'success': False,
                    'error': f'在文件夹中未找到CSV文件: {input_folder}'
                }

            print(f"发现 {len(csv_files)} 个CSV文件")

            # 2. 批量处理
            results = {}
            for i, csv_file in enumerate(csv_files, 1):
                file_name = os.path.basename(csv_file)
                print(f"处理文件 ({i}/{len(csv_files)}): {file_name}")

                result = self.fusion_engine.predict_single_file(csv_file)
                results[file_name] = result

                # 显示处理结果
                if result.get('success', False):
                    print(f"  ✓ 成功 - 风险: {result['risk']:.3f}, 标签: {result['label']}")
                else:
                    print(f"  ✗ 失败 - {result.get('error', '未知错误')}")

            # 3. 结果汇总
            summary = self.result_aggregator.aggregate_results(results)

            # 4. 保存结果
            if output_folder:
                self.save_results(results, summary, output_folder)

            return {
                'success': True,
                'processed_files': len(csv_files),
                'results': results,
                'summary': summary
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'批量处理失败: {str(e)}'
            }

    def save_results(self, results: Dict, summary: Dict, output_folder: str):
        """保存处理结果"""
        try:
            os.makedirs(output_folder, exist_ok=True)

            # 保存详细结果
            if self.config['output']['save_detailed_results']:
                results_file = os.path.join(output_folder, 'detailed_results.json')
                with open(results_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"详细结果已保存: {results_file}")

            # 保存汇总报告
            if self.config['output']['save_summary_report']:
                summary_file = os.path.join(output_folder, 'summary_report.json')
                with open(summary_file, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, ensure_ascii=False, indent=2)
                print(f"汇总报告已保存: {summary_file}")

                # 生成CSV格式的简化报告
                csv_report = self.generate_csv_report(results)
                csv_file = os.path.join(output_folder, 'results_summary.csv')
                csv_report.to_csv(csv_file, index=False, encoding='utf-8')
                print(f"CSV报告已保存: {csv_file}")

        except Exception as e:
            print(f"保存结果失败: {e}")

    def generate_csv_report(self, results: Dict) -> pd.DataFrame:
        """生成CSV格式的报告"""
        report_data = []

        for file_name, result in results.items():
            row = {
                'file_name': file_name,
                'success': result.get('success', False),
                'risk': result.get('risk', 0.0),
                'label': result.get('label', 0),
                'confidence': result.get('confidence', 0.0),
                'error': result.get('error', '')
            }

            # 添加各算法的结果
            algorithm_results = result.get('algorithm_results', {})
            for algo_name in ['precursor', 'anomaly', 'expert']:
                algo_result = algorithm_results.get(algo_name, {})
                row[f'{algo_name}_success'] = algo_result.get('success', False)
                row[f'{algo_name}_risk'] = algo_result.get('risk', 0.0)
                row[f'{algo_name}_label'] = algo_result.get('label', 0)

            report_data.append(row)

        return pd.DataFrame(report_data)

class FileScanner:
    """文件扫描器"""

    def scan_csv_files(self, folder_path: str) -> List[str]:
        """扫描文件夹中的CSV文件"""
        csv_files = []

        try:
            folder_path = Path(folder_path)
            if not folder_path.exists():
                return csv_files

            # 递归扫描CSV文件
            for csv_file in folder_path.rglob('*.csv'):
                if csv_file.is_file():
                    csv_files.append(str(csv_file))

            # 按文件名排序
            csv_files.sort()

        except Exception as e:
            print(f"文件扫描失败: {e}")

        return csv_files

class ResultAggregator:
    """结果汇总器"""

    def aggregate_results(self, batch_results: Dict) -> Dict:
        """汇总批量处理结果"""
        try:
            total_files = len(batch_results)
            successful_files = sum(1 for r in batch_results.values() if r.get('success', False))

            # 统计各算法成功率
            algorithm_stats = {}
            for algo in ['precursor', 'anomaly', 'expert']:
                algo_success = sum(
                    1 for r in batch_results.values()
                    if r.get('algorithm_results', {}).get(algo, {}).get('success', False)
                )
                algorithm_stats[algo] = {
                    'success_rate': algo_success / total_files if total_files > 0 else 0,
                    'success_count': algo_success
                }

            # 风险分布统计
            successful_results = [r for r in batch_results.values() if r.get('success', False)]

            if successful_results:
                risks = [r['risk'] for r in successful_results]
                labels = [r['label'] for r in successful_results]

                risk_stats = {
                    'mean_risk': np.mean(risks),
                    'max_risk': max(risks),
                    'min_risk': min(risks),
                    'std_risk': np.std(risks)
                }

                label_stats = {
                    'alert_count': sum(labels),
                    'alert_rate': np.mean(labels),
                    'normal_count': len(labels) - sum(labels)
                }
            else:
                risk_stats = {
                    'mean_risk': 0.0,
                    'max_risk': 0.0,
                    'min_risk': 0.0,
                    'std_risk': 0.0
                }

                label_stats = {
                    'alert_count': 0,
                    'alert_rate': 0.0,
                    'normal_count': 0
                }

            return {
                'total_files': total_files,
                'successful_files': successful_files,
                'success_rate': successful_files / total_files if total_files > 0 else 0,
                'algorithm_stats': algorithm_stats,
                'risk_stats': risk_stats,
                'label_stats': label_stats,
                'processing_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'error': f'结果汇总失败: {str(e)}',
                'processing_timestamp': datetime.now().isoformat()
            }
```

### 6. 主入口 (main.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一钻井检测系统V2.0 - 主入口
"""

import os
import sys
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config import config_manager
from core.fusion_engine import UnifiedFusionEngine
from core.batch_processor import BatchProcessingManager

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一钻井检测系统V2.0')
    parser.add_argument('--input', '-i', required=True, help='输入文件或文件夹路径')
    parser.add_argument('--output', '-o', help='输出文件夹路径')
    parser.add_argument('--mode', '-m', choices=['single', 'batch'], default='auto',
                       help='处理模式：single(单文件), batch(批量), auto(自动检测)')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--weights', help='算法权重，格式：precursor:0.5,anomaly:0.3,expert:0.2')

    args = parser.parse_args()

    # 加载配置
    if args.config:
        config_manager.config_path = args.config
        config_manager.config = config_manager.load_config()

    # 解析权重
    weights = None
    if args.weights:
        try:
            weight_pairs = args.weights.split(',')
            weights = {}
            for pair in weight_pairs:
                algo, weight = pair.split(':')
                weights[algo.strip()] = float(weight.strip())
        except Exception as e:
            print(f"权重解析失败: {e}")
            return 1

    # 确定处理模式
    if args.mode == 'auto':
        if os.path.isfile(args.input):
            mode = 'single'
        elif os.path.isdir(args.input):
            mode = 'batch'
        else:
            print(f"输入路径不存在: {args.input}")
            return 1
    else:
        mode = args.mode

    # 创建融合引擎
    fusion_engine = UnifiedFusionEngine(weights=weights)

    print(f"统一钻井检测系统V2.0")
    print(f"处理模式: {mode}")
    print(f"输入路径: {args.input}")
    print(f"输出路径: {args.output or '控制台输出'}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)

    try:
        if mode == 'single':
            # 单文件处理
            result = fusion_engine.predict_single_file(args.input)
            print_single_result(args.input, result)

            # 保存结果
            if args.output:
                save_single_result(result, args.output)

        elif mode == 'batch':
            # 批量处理
            batch_processor = BatchProcessingManager(fusion_engine)
            result = batch_processor.process_folder(args.input, args.output)
            print_batch_result(result)

        print("-" * 50)
        print(f"处理完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return 0

    except Exception as e:
        print(f"处理失败: {e}")
        return 1

def print_single_result(file_path: str, result: dict):
    """打印单文件结果"""
    print(f"文件: {os.path.basename(file_path)}")

    if result.get('success', False):
        print(f"  状态: ✓ 成功")
        print(f"  风险分数: {result['risk']:.4f}")
        print(f"  预测标签: {result['label']} ({'预警' if result['label'] == 1 else '正常'})")
        print(f"  置信度: {result.get('confidence', 0):.4f}")

        # 显示各算法结果
        algorithm_results = result.get('algorithm_results', {})
        print(f"  算法详情:")
        for algo_name, algo_result in algorithm_results.items():
            status = "✓" if algo_result.get('success', False) else "✗"
            risk = algo_result.get('risk', 0)
            label = algo_result.get('label', 0)
            print(f"    {algo_name}: {status} 风险={risk:.4f} 标签={label}")
    else:
        print(f"  状态: ✗ 失败")
        print(f"  错误: {result.get('error', '未知错误')}")

def print_batch_result(result: dict):
    """打印批量处理结果"""
    if result.get('success', False):
        summary = result.get('summary', {})
        print(f"批量处理完成:")
        print(f"  总文件数: {summary.get('total_files', 0)}")
        print(f"  成功文件数: {summary.get('successful_files', 0)}")
        print(f"  成功率: {summary.get('success_rate', 0):.2%}")

        # 风险统计
        risk_stats = summary.get('risk_stats', {})
        print(f"  风险统计:")
        print(f"    平均风险: {risk_stats.get('mean_risk', 0):.4f}")
        print(f"    最大风险: {risk_stats.get('max_risk', 0):.4f}")
        print(f"    最小风险: {risk_stats.get('min_risk', 0):.4f}")

        # 标签统计
        label_stats = summary.get('label_stats', {})
        print(f"  预警统计:")
        print(f"    预警文件数: {label_stats.get('alert_count', 0)}")
        print(f"    预警率: {label_stats.get('alert_rate', 0):.2%}")

        # 算法统计
        algorithm_stats = summary.get('algorithm_stats', {})
        print(f"  算法成功率:")
        for algo_name, stats in algorithm_stats.items():
            print(f"    {algo_name}: {stats.get('success_rate', 0):.2%}")
    else:
        print(f"批量处理失败: {result.get('error', '未知错误')}")

def save_single_result(result: dict, output_path: str):
    """保存单文件结果"""
    import json

    try:
        os.makedirs(output_path, exist_ok=True)
        result_file = os.path.join(output_path, 'result.json')

        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"结果已保存: {result_file}")

    except Exception as e:
        print(f"保存结果失败: {e}")

if __name__ == '__main__':
    sys.exit(main())

### 7. 配置文件 (config.yaml)

```yaml
# 统一钻井检测系统V2.0配置文件

system:
  version: "2.0"
  name: "统一钻井检测系统V2.0"

# 算法权重配置
weights:
  precursor: 0.5    # 前驱信号检测权重
  anomaly: 0.3      # 异常检测权重
  expert: 0.2       # 专家规则权重

# 算法配置
algorithms:
  # 前驱信号检测配置
  precursor:
    model_path: "./前驱信号检测/checkpoints"
    data_path: "./前驱信号检测/dataset2"
    sequence_length: 152
    features: 10

  # 异常检测配置（简化版）
  anomaly:
    model_path: "./异常检测/checkpoints"
    train_data_path: "./异常检测/dataset/train自201H54-3.npy"
    anomaly_ratio: 1.0
    features: 12

  # 专家规则配置（纯规则版）
  expert:
    window_size: 30
    score_threshold: 4
    rules_config:
      depth_change_threshold: 0.001
      bit_change_threshold: 0.001
      rpm_threshold: 5
      hook_height_change_threshold: 0.01
      hookload_change_threshold: 3
      flow_diff_threshold: 15

# 数据处理配置
data_processing:
  csv_encoding: "utf-8"
  npy_precision: "float32"
  missing_value_strategy: "default"

# 输出配置
output:
  save_detailed_results: true
  save_summary_report: true
  result_format: "json"

### 8. 依赖包 (requirements.txt)

```
# 统一钻井检测系统V2.0依赖包

# 基础科学计算
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# 配置文件处理
PyYAML>=6.0

# 深度学习框架（根据原算法要求）
torch>=1.9.0
torchvision>=0.10.0

# 其他工具
pathlib2>=2.3.0
tqdm>=4.62.0
```

## 🚀 使用指南

### 1. 安装和配置

```bash
# 1. 创建项目目录
mkdir unified_detection_system_v2
cd unified_detection_system_v2

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置算法路径
# 编辑config.yaml，确保算法路径正确
```

### 2. 单文件处理

```bash
# 基本用法
python main.py --input data/test.csv --output results/

# 指定权重
python main.py --input data/test.csv --output results/ --weights "precursor:0.6,anomaly:0.2,expert:0.2"

# 使用自定义配置
python main.py --input data/test.csv --output results/ --config custom_config.yaml
```

### 3. 批量处理

```bash
# 处理整个文件夹
python main.py --input data_folder/ --output results/ --mode batch

# 自动检测模式（推荐）
python main.py --input data_folder/ --output results/
```

### 4. 输出结果说明

#### 单文件输出
```json
{
  "success": true,
  "risk": 0.234,
  "label": 0,
  "confidence": 1.0,
  "algorithm_results": {
    "precursor": {"success": true, "risk": 0.12, "label": 0},
    "anomaly": {"success": true, "risk": 0.45, "label": 1},
    "expert": {"success": true, "risk": 0.18, "label": 0}
  },
  "fusion_weights": {"precursor": 0.5, "anomaly": 0.3, "expert": 0.2}
}
```

#### 批量处理输出
- `detailed_results.json`: 每个文件的详细结果
- `summary_report.json`: 汇总统计报告
- `results_summary.csv`: CSV格式的简化报告

## 📊 系统特点

### 1. 核心改进
- **简化异常检测**: 去除不稳定的经验卡钻检测，只保留重建误差阈值方法
- **纯规则专家系统**: 不依赖外部机器学习模型，使用6个核心规则计算
- **统一接口设计**: 三个算法使用完全一致的预测接口
- **批量处理能力**: 原生支持文件夹级别的批量处理

### 2. 可靠性保证
- **分层错误处理**: 算法级、融合级、系统级三层错误捕获
- **优雅降级**: 单算法失败时自动调整权重继续运行
- **临时文件管理**: 自动清理处理过程中的临时文件
- **详细日志记录**: 完整的处理过程和错误信息记录

### 3. 易用性设计
- **命令行接口**: 简单易用的命令行参数
- **配置文件驱动**: 灵活的YAML配置文件
- **多种输出格式**: JSON和CSV格式的结果输出
- **进度显示**: 批量处理时的实时进度显示

这个V2.0实现方案确保了系统的稳定性和可运行性，专注于功能的正确实现而不过度关注性能优化。
```
