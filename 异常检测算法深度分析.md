# 异常检测算法深度分析

## 📋 概述

基于对异常检测run.py及其完整调用链的深入分析，本文档详细说明该算法的真实运行机制、数据要求和输出格式，确保融合算法不会出错。

## 🔍 运行流程分析

### 1. 主入口：run.py

#### 关键参数配置
```bash
python run.py \
  --task_name anomaly_detection \
  --is_training 0 \
  --root_path ./dataset/test \
  --model_id 泸203H11-2 \
  --model FEDformer \
  --data anomaly_detection \
  --features M \
  --seq_len 96 \
  --label_len 48 \
  --pred_len 96 \
  --enc_in 12 \
  --dec_in 12 \
  --c_out 12 \
  --d_model 128 \
  --d_ff 512 \
  --test_file 测试数据.npy \
  --no_label
```

#### 执行路径（is_training=0时）
```python
# run.py 第164-188行
if args.is_training:
    # 训练流程...
else:
    ii = 0
    setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
        args.task_name,      # anomaly_detection
        args.model_id,       # 泸203H11-2
        args.model,          # FEDformer
        args.data,           # anomaly_detection
        args.features,       # M
        args.seq_len,        # 96
        args.label_len,      # 48
        args.pred_len,       # 96
        args.d_model,        # 128
        args.n_heads,        # 8
        args.e_layers,       # 3
        args.d_layers,       # 1
        args.d_ff,           # 512
        args.factor,         # 5
        args.embed,          # timeF
        args.distil,         # True
        args.des,            # Exp
        ii                   # 0
    )
    
    exp = Exp_Anomaly_Detection(args)  # 创建实验对象
    print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
    exp.test(setting, test=1)          # 执行测试
```

### 2. 数据提供器：data_provider

#### 数据类型映射（data_factory.py）
```python
data_dict = {
    'anomaly_detection': Ning209H83Loader,  # 关键映射
    # 其他数据类型...
}

def data_provider(args, flag):
    Data = data_dict[args.data]  # 获取Ning209H83Loader
    
    if args.task_name == 'anomaly_detection':
        drop_last = False
        data_set = Data(
            root_path=args.root_path,
            win_size=args.seq_len,
            flag=flag,
            test_file=args.test_file,      # 关键：测试文件名
            label_file=args.label_file,    # 标签文件名
            no_label=getattr(args, 'no_label', False)  # 无标签模式
        )
        data_loader = DataLoader(
            data_set,
            batch_size=args.batch_size,
            shuffle=(flag != 'test'),
            drop_last=False
        )
    
    return data_set, data_loader
```

### 3. 核心数据加载器：Ning209H83Loader

#### 初始化流程
```python
class Ning209H83Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train", test_file=None, label_file=None, no_label=False):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        self.no_label = no_label
        
        # 1. 加载训练数据进行标准化
        train_data = np.load(os.path.join(root_path, "train自201H54-3.npy"), allow_pickle=True)
        print(f"加载训练数据: {os.path.join(root_path, 'train自201H54-3.npy')}")
        
        # 去除最后一列（时间戳）
        train_data = train_data[:, :-1]
        
        # 训练StandardScaler
        self.scaler.fit(train_data)
        data = self.scaler.transform(train_data)
        
        # 2. 加载测试数据
        test_file_name = test_file if test_file else "24井实时数据.npy"
        
        try:
            test_data_path = os.path.join(root_path, test_file_name)
            test_data = np.load(test_data_path, allow_pickle=True)
            print(f"成功加载测试数据: {test_data_path}")
        except Exception as e:
            print(f"加载指定测试数据失败: {e}")
            # 尝试默认路径
            try:
                default_test_path = os.path.join("./dataset/test", test_file_name)
                test_data = np.load(default_test_path, allow_pickle=True)
                print(f"从默认路径成功加载测试数据: {default_test_path}")
            except Exception as e2:
                print(f"所有测试数据加载尝试均失败: {e2}")
                # 创建假数据避免崩溃
                test_data = np.zeros((100, 13))
                print("使用空测试数据代替")
```

#### 数据格式要求
```python
        # 关键发现：NPY文件格式要求
        print(f"测试数据形状: {test_data.shape}")  # 应该是 (n_samples, 13)
        
        # 提取时间戳（最后一列）
        timestamp = test_data[:, -1]
        
        # 去除时间戳，保留12维特征
        test_data = test_data[:, :-1]  # 现在是 (n_samples, 12)
        
        # 应用训练时的标准化
        self.test = self.scaler.transform(test_data)
        self.train = data
        
        # 生成时间戳
        self.test_timestamps = timestamp
```

#### 标签处理（关键）
```python
        # 3. 标签处理（只在非无标签模式下）
        if not no_label:
            label_file_name = label_file if label_file else f"{os.path.splitext(test_file_name)[0]}_label.npy"
            try:
                # 首先在root_path中查找标签文件
                label_path = os.path.join(root_path, label_file_name)
                if os.path.exists(label_path):
                    self.test_labels = np.load(label_path, allow_pickle=True)
                    print(f"标签文件已加载: {label_path}")
                else:
                    # 尝试在默认位置查找
                    default_label_path = os.path.join("./dataset/test", label_file_name)
                    if os.path.exists(default_label_path):
                        self.test_labels = np.load(default_label_path, allow_pickle=True)
                        print(f"从默认位置加载标签: {default_label_path}")
                    else:
                        raise FileNotFoundError(f"找不到标签文件: {label_path}, {default_label_path}")
            except Exception as e:
                print(f"警告: 加载标签文件失败: {e}")
                # 创建一个零标签数组作为替代
                self.test_labels = np.zeros(len(test_data))
        else:
            # 无标签模式：创建虚拟标签
            self.test_labels = np.zeros(len(test_data))
            print("无标签模式: 使用虚拟标签")
```

#### 数据获取方法
```python
    def __getitem__(self, index):
        if self.flag == "train":
            return self.train[index], self.train[index]
        elif self.flag == "val":
            return self.val[index], self.val[index]
        elif self.flag == "test":
            return self.test[index], self.test_labels[index], self.test_timestamps[index]
        else:
            return self.test[index], self.test_labels[index], self.test_timestamps[index]
```

### 4. 测试流程：exp.test()

#### 数据加载和模型初始化
```python
def test(self, setting, test=0):
    # 1. 数据加载
    test_data, test_loader = self._get_data(flag='test')
    train_data, train_loader = self._get_data(flag='train')
    
    if test:
        print('loading model')
        # 加载预训练模型
        self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))

    # 检查无标签模式
    no_label_mode = getattr(self.args, 'no_label', False)
    if no_label_mode:
        print("无标签模式: 仅进行预测，不评估性能指标")
```

#### 训练集重建误差计算
```python
    # 2. 训练集重建误差计算（用于阈值确定）
    self.model.eval()
    self.anomaly_criterion = nn.MSELoss(reduce=False)
    
    train_energy = []
    with torch.no_grad():
        for i, (batch_x, batch_y) in enumerate(train_loader):
            batch_x = batch_x.float().to(self.device)
            # reconstruction
            outputs = self.model(batch_x, None, None, None)
            # criterion
            score = torch.mean(self.anomaly_criterion(batch_x, outputs), dim=-1)
            train_energy.append(score.detach().cpu().numpy())
    
    train_energy = np.concatenate(train_energy, axis=0).reshape(-1)
```

#### 测试集重建误差计算
```python
    # 3. 测试集重建误差计算
    attens_energy = []
    test_labels = []
    test_timestamps = []
    raw_test_data = []  # 保存原始数据用于专业卡钻检测
    
    with torch.no_grad():
        for i, (batch_x, batch_y, test_timestamp) in enumerate(test_loader):
            batch_x = batch_x.float().to(self.device)
            # 保存原始数据
            raw_test_data.append(batch_x.cpu().numpy())

            # reconstruction
            outputs = self.model(batch_x, None, None, None)
            # criterion
            score = torch.mean(self.anomaly_criterion(batch_x, outputs), dim=-1)
            score = score.detach().cpu().numpy()
            attens_energy.append(score)
            test_labels.append(batch_y)
            test_timestamps.append(test_timestamp.numpy().astype('datetime64[s]'))

    attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
    test_energy = np.array(attens_energy)
```

#### 阈值计算和异常检测
```python
    # 4. 阈值计算
    combined_energy = np.concatenate([train_energy, test_energy], axis=0)
    threshold = np.percentile(combined_energy, 100 - self.args.anomaly_ratio)
    print("重建误差阈值:", threshold)

    # 5. 专业卡钻检测或阈值方法
    use_stuck_pipe_detector = True  # 控制开关
    
    if use_stuck_pipe_detector:
        try:
            from stuck_pipe_detector import StuckPipeDetector
            
            if raw_test_data:
                # 合并所有批次的原始数据
                all_raw_data = np.concatenate(raw_test_data, axis=0)
                
                # 创建检测器实例
                detector = StuckPipeDetector()
                
                # 执行专业卡钻检测
                print("正在执行经验卡钻检测...")
                anomalies, scores = detector.detect(all_raw_data, torque_idx, hook_load_idx)

                # 检查异常比例是否合理
                anomaly_ratio = np.mean(anomalies)
                print(f"经验卡钻检测异常比例: {anomaly_ratio * 100:.2f}%")
                
                if 0.0001 <= anomaly_ratio <= 0.45:  # 合理范围
                    print("使用经验卡钻检测结果")
                    pred = anomalies
                    test_energy = scores  # 更新异常分数
                else:
                    print("经验卡钻检测异常比例不合理，回退到重建误差方法")
                    pred = (test_energy > threshold).astype(int)
            else:
                print("没有获取到原始测试数据，使用重建误差方法")
                pred = (test_energy > threshold).astype(int)
        except Exception as e:
            print(f"经验卡钻检测失败: {e}，回退到重建误差方法")
            pred = (test_energy > threshold).astype(int)
    else:
        # 直接使用重建误差方法
        print("专业卡钻检测功能已关闭，使用重建误差方法")
        pred = (test_energy > threshold).astype(int)
```

### 5. 关键输出解析

#### 全局变量输出
```python
# 关键：异常比例记录到全局变量
global ANOMALY_RATIO_SUMMARY

# 记录异常比例
ANOMALY_RATIO_SUMMARY[test_file_base] = {
    'ratio': anomaly_ratio * 100,  # 百分比
    'valid': True  # 是否使用了经验卡钻检测
}
```

#### 控制台输出格式
```python
# 关键输出信息
print(f"经验卡钻检测异常比例: {anomaly_ratio * 100:.2f}%")
print("使用经验卡钻检测结果")
# 或
print("经验卡钻检测异常比例不合理，回退到重建误差方法")
```

## 🎯 融合系统关键发现

### 1. 数据格式要求

```python
# NPY文件格式（关键）
data_format = {
    'file_type': '.npy',
    'shape': '(n_samples, 13)',  # 12个特征 + 1个时间戳
    'features': '前12列为数值特征，第13列为时间戳',
    'data_type': 'float64',
    'encoding': 'numpy binary format'
}

# 训练数据要求
train_data_requirement = {
    'file_name': 'train自201H54-3.npy',  # 固定文件名
    'location': 'root_path目录下',
    'purpose': '用于StandardScaler训练和阈值计算'
}
```

### 2. 输出解析策略

```python
def parse_anomaly_output(stdout_text):
    """解析异常检测输出"""
    result = {'success': False}
    
    # 关键：提取经验卡钻检测异常比例
    ratio_match = re.search(r'经验卡钻检测异常比例:\s*(\d+\.\d+)%', stdout_text)
    if ratio_match:
        anomaly_ratio = float(ratio_match.group(1))
        result['anomaly_ratio'] = anomaly_ratio
        result['risk'] = anomaly_ratio / 100.0  # 转换为0-1风险分数
        result['label'] = 1 if anomaly_ratio > 10.0 else 0  # 阈值判断
        result['success'] = True
        
        # 检查是否使用了经验卡钻检测
        if "使用经验卡钻检测结果" in stdout_text:
            result['method'] = 'expert_stuck_detection'
            result['confidence'] = 0.9  # 高置信度
        else:
            result['method'] = 'threshold_detection'
            result['confidence'] = 0.6  # 中等置信度
    else:
        # 没有找到异常比例，可能是其他错误
        result['anomaly_ratio'] = 0.0
        result['risk'] = 0.0
        result['label'] = 0
        result['method'] = 'failed'
        result['confidence'] = 0.1
    
    return result
```

### 3. CSV到NPY转换

```python
def convert_csv_to_npy_for_anomaly(csv_file, output_npy):
    """将CSV转换为异常检测所需的NPY格式"""
    df = pd.read_csv(csv_file)
    
    # 选择标准特征（前9个）
    standard_features = ['DEP', 'BITDEP', 'HOKHEI', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'DRITIME']
    
    # 确保所有特征都存在
    for feature in standard_features:
        if feature not in df.columns:
            df[feature] = 0.0  # 填充缺失特征
    
    data = df[standard_features].values
    
    # 扩展到12维（添加3个衍生特征）
    derived_1 = data[:, 3] * data[:, 4]  # WOB * HKLD
    derived_2 = data[:, 4] * data[:, 5]  # HKLD * RPM  
    derived_3 = data[:, 5] * data[:, 6]  # RPM * TOR
    
    # 组合成12维特征
    features_12d = np.column_stack([data, derived_1, derived_2, derived_3])
    
    # 添加时间戳列（第13列）
    if 'date' in df.columns:
        # 尝试解析时间戳
        try:
            timestamps = pd.to_datetime(df['date']).astype(np.int64) // 10**9
        except:
            timestamps = np.arange(len(features_12d))
    else:
        timestamps = np.arange(len(features_12d))
    
    # 组合最终数据：12维特征 + 1维时间戳
    final_data = np.column_stack([features_12d, timestamps])
    
    # 保存为NPY格式
    np.save(output_npy, final_data)
    print(f"已转换并保存到: {output_npy}")
    print(f"数据形状: {final_data.shape}")  # 应该是 (n_samples, 13)
    
    return output_npy
```

### 4. 融合接口设计

```python
class AnomalyDetectionAdapter:
    def __init__(self, temp_dir):
        self.temp_dir = temp_dir
        self.train_file = "train自201H54-3.npy"  # 固定训练文件名
    
    def predict_single_file(self, csv_file):
        """预测单个CSV文件"""
        
        # 1. 确保训练文件存在
        train_path = os.path.join(self.temp_dir, self.train_file)
        if not os.path.exists(train_path):
            # 创建虚拟训练文件或从默认位置复制
            self._prepare_train_file(train_path)
        
        # 2. 转换CSV为NPY格式
        test_file_name = "temp_test.npy"
        test_path = os.path.join(self.temp_dir, test_file_name)
        convert_csv_to_npy_for_anomaly(csv_file, test_path)
        
        # 3. 构建命令行参数
        cmd = [
            'python', '异常检测/run.py',
            '--task_name', 'anomaly_detection',
            '--is_training', '0',
            '--root_path', self.temp_dir,
            '--model', 'FEDformer',
            '--data', 'anomaly_detection',
            '--enc_in', '12',
            '--test_file', test_file_name,
            '--no_label'
        ]
        
        # 4. 执行并解析结果
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='异常检测')
        
        # 5. 解析输出
        parsed_result = parse_anomaly_output(result.stdout)
        
        # 6. 清理临时文件
        if os.path.exists(test_path):
            os.remove(test_path)
        
        return parsed_result
```

## 📊 总结

### 异常检测算法的核心特点：

1. **NPY格式要求**：必须是NPY文件，不是CSV
2. **固定训练文件**：需要`train自201H54-3.npy`进行标准化
3. **12+1维数据**：12个特征 + 1个时间戳
4. **双重检测机制**：经验卡钻检测 + 重建误差阈值
5. **异常比例输出**：关键输出是百分比形式的异常比例

### 融合系统设计要点：

1. **数据转换**：CSV → NPY格式转换
2. **训练文件管理**：确保训练文件存在
3. **输出解析**：正确提取"经验卡钻检测异常比例"
4. **错误处理**：处理文件不存在、格式错误等异常
5. **临时文件管理**：及时清理转换的NPY文件

这个分析为融合系统提供了准确的异常检测集成方案，确保不会出错。
